package org.jeecg.modules.station.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.excommons.utils.XmlEscapeUtils;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.system.service.ISysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 体检小项结果表
 * @Author: jeecg-boot
 * @Date: 2024-04-21
 * @Version: V1.0
 */
@Api(tags = "体检小项结果表")
@RestController
@RequestMapping("/station/customerRegItemResult")
@Slf4j
public class CustomerRegItemResultController extends JeecgController<CustomerRegItemResult, ICustomerRegItemResultService> {
    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;
    @Autowired
    private ICustomerRegItemGroupService itemGroupService;
    @Autowired
    private ICustomerRegDepartSummaryService customerRegDepartSummaryService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private CustomerRegMapper customerRegMapper;

    /**
     * 根据体检登记ID查询未检的大项
     * @param regId
     * @return
     */
    @ApiOperation(value = "体检小项结果表-根据体检登记ID查询未检的大项", notes = "体检小项结果表-根据体检登记ID查询未检的大项")
    @GetMapping(value = "/listUncheckedGroupByRegId")
    public Result<?> listUncheckedGroupByRegId(String regId) {
        List<CustomerRegItemGroup> groupList = customerRegItemResultService.listUncheckGroupByRegId(regId);
        return Result.OK(groupList);
    }


    @ApiOperation(value = "体检小项结果表-fixData", notes = "体检小项结果表-fixData")
    @GetMapping(value = "/fixData")
    public Result<?> fixData() {
        long start = System.currentTimeMillis();
        QueryWrapper<CustomerRegDepartSummary> departSummaryQuery = new QueryWrapper<>();
        departSummaryQuery.ne("update_by_manual", "1");
        List<CustomerRegDepartSummary> departSummaryList = customerRegDepartSummaryService.list(departSummaryQuery);
        departSummaryList.forEach(ds -> {
            String departId = ds.getDepartmentId();
            String customerRegId = ds.getCustomerRegId();

            CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
            if (customerReg != null) {
                customerRegDepartSummaryService.generateAndSaveSummary(departId, customerReg);
                /*QueryWrapper<CustomerRegItemResult> resultQueryWrapper = new QueryWrapper<>();
                resultQueryWrapper.eq("department_id", departId);
                resultQueryWrapper.eq("customer_reg_id", customerRegId);
                List<CustomerRegItemResult> itemResultList = customerRegItemResultService.list(resultQueryWrapper);
                customerRegDepartSummaryService.generateSummary(departId,customerReg,itemResultList);*/
            }

            String departSummary = ds.getCharacterSummary();
            if (StringUtils.isNotBlank(departSummary)) {
                ds.setCharacterSummary(XmlEscapeUtils.unescapeXml(departSummary));
                customerRegDepartSummaryService.updateById(ds);
            }
        });

        long endTime = System.currentTimeMillis();

        System.out.println("success:" + (endTime - start));


       /* QueryWrapper<CustomerRegItemGroup> groupQueryWrapper = new QueryWrapper<>();
        List<String> departmentNameList = new ArrayList<>();
        departmentNameList.add("检验检查项目");
        departmentNameList.add("检验科");
        groupQueryWrapper.in("department_name", departmentNameList);

        List<CustomerRegItemGroup> allGroupList = itemGroupService.list(groupQueryWrapper);

        allGroupList.forEach(group -> {

            QueryWrapper<CustomerRegItemResult> resultQueryWrapper = new QueryWrapper<>();
            resultQueryWrapper.eq("group_his_code", group.getHisCode());
            resultQueryWrapper.eq("customer_reg_id", group.getCustomerRegId());

            //resultQueryWrapper.last("limit 1");
            List<CustomerRegItemResult> resultList = customerRegItemResultService.list(resultQueryWrapper);

            Set<String> abnormalFlagSet = new HashSet<>();
            List<String> checkConclusionList = new ArrayList<>();
            for (CustomerRegItemResult result : resultList) {
                String abnormalFlag = result.getAbnormalFlag();
                String abnormalDesc = result.getAbnormalFlagDesc();
                String unit = result.getUnit();
                String value = result.getValue();
                String itemName = result.getItemHisName();

                if (StringUtils.equals(abnormalFlag, "1")) {
                    checkConclusionList.add(itemName + abnormalDesc + "(" + value + unit + ")");
                }
                abnormalFlagSet.add(abnormalFlag);
            }

            String groupAbnormalFlag = abnormalFlagSet.contains("1") ? "1" : "0";
            group.setAbnormalFlag(groupAbnormalFlag);
            group.setCheckConclusion(StringUtils.join(checkConclusionList, ";"));

            customerRegItemGroupService.updateById(group);

           *//* CustomerRegItemResult result = customerRegItemResultService.getOne(resultQueryWrapper);
            if (result != null && result.getCreateTime() != null) {

                group.setReportTime(result.getCreateTime());
                group.setReportDoctorCode(result.getDoctorId());
                group.setReportDoctorName(result.getDoctorName());
                group.setAuditDoctorCode(result.getDoctorId());
                group.setAuditDoctorName(result.getDoctorName());
                group.setCheckDoctorCode(result.getDoctorId());
                group.setCheckDoctorName(result.getDoctorName());
                group.setCheckBillNo(result.getCheckBillNo());
                if (StringUtils.isNotBlank(result.getReportPdf())) {
                    group.setReportPdf(result.getReportPdf());
                }
                if (result.getPic() != null && !result.getPic().isEmpty()) {
                    group.setPic(result.getPic());
                    group.setReportPics(result.getPic());
                }

                itemGroupService.updateById(group);
            }*//*
        });
*/
        return Result.OK();
    }

    @ApiOperation(value = "体检小项结果表-同步检查数据", notes = "体检小项结果表-同步检查数据")
    @GetMapping(value = "/fetchCheckData")
    public Result<?> fetchCheckData(String regId, String startTime, String endTime) {
        try {
            customerRegItemResultService.fetchCheckData(regId, startTime, endTime);
            return Result.OK();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "体检小项结果表-分页列表查询", notes = "体检小项结果表-分页列表查询")
    @GetMapping(value = "/fetchLisData")
    public Result<?> fetchLisData(String regId, String startTime, String endTime) {
        try {
            customerRegItemResultService.fetchLisData(regId, startTime, endTime);
            return Result.OK();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "体检小项结果表-获取lis结果", notes = "体检小项结果表-分页列表查询")
    @GetMapping(value = "/listGroupByRegId")
    public Result<?> listGroupByRegId(String regId, String departmentIds,
                                     @RequestParam(defaultValue = "false") boolean includeDependencies) {
        List<String> departmentIdList = null;
        if (StringUtils.isNotBlank(departmentIds)) {
            departmentIdList = Arrays.asList(departmentIds.split(","));
        }
        List<CustomerRegItemGroup> groupList = customerRegItemResultService.listGroupByRegId(regId, departmentIdList, includeDependencies);
        return Result.OK(groupList);
    }

    @ApiOperation(value = "体检小项结果表-保存图片", notes = "体检小项结果表-保存图片")
    @PostMapping(value = "/saveReportPics")
    public Result<?> saveReportPics(@RequestBody JSONObject info) {
        String customerRegItemGroupId = info.getString("id");
        JSONArray pics = info.getJSONArray("reportPics");
        List<String> reportPics = pics.toJavaList(String.class);
        CustomerRegItemGroup group = customerRegItemGroupService.getById(customerRegItemGroupId);
        group.setReportPics(reportPics);
        customerRegItemGroupService.updateById(group);

        return Result.OK();
    }

    /**
     * 保存结果
     *
     * @param info
     * @return
     */
    @AutoLog(value = "体检小项结果表-保存结果")
    @ApiOperation(value = "体检小项结果表-保存结果", notes = "体检小项结果表-保存结果")
    @RequiresPermissions("station:customer_reg_item_result:add")
    @PostMapping(value = "/saveItemResult")
    public Result<?> saveItemResult(@RequestBody JSONObject info) {
        JSONArray list = info.getJSONArray("resultList");
        List<CustomerRegItemResult> resultList = list.toJavaList(CustomerRegItemResult.class);
        CustomerReg reg = info.getObject("reg", CustomerReg.class);
        Boolean isTempSave = info.getBoolean("isTempSave");

        if (customerRegDepartSummaryService.isSummaryAudited(reg.getId())) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }

        customerRegItemResultService.saveItemResult(resultList, reg,isTempSave);

        String departmentId = info.getString("departmentId");
        if (reg != null && StringUtils.isNotBlank(departmentId)) {
            List<CustomerRegCriticalItem> criticalItems = customerRegDepartSummaryService.generateCriticalItem(departmentId, reg, resultList);
            return Result.OK("保存成功！", criticalItems);
        } else {
            return Result.OK("保存成功！");
        }
    }


    /**
     * 反放弃
     *
     * @param customerRegItemResult
     * @return
     */
    @AutoLog(value = "体检小项结果表-反放弃")
    @ApiOperation(value = "体检小项结果表-反放弃", notes = "体检小项结果表-反放弃")
    @RequiresPermissions("station:customer_reg_item_result:abandon")
    @PostMapping(value = "/unAbandon")
    public Result<?> unAbandon(@RequestBody CustomerRegItemResult customerRegItemResult) {
        if (customerRegDepartSummaryService.isSummaryAudited(customerRegItemResult.getCustomerRegId())) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.unAbandonItemResult(customerRegItemResult);
        return Result.OK("保存成功！");
    }


    /**
     * 放弃项目
     *
     * @param customerRegItemResult
     * @return
     */
    @AutoLog(value = "体检小项结果表-放弃项目")
    @ApiOperation(value = "体检小项结果表-放弃项目", notes = "体检小项结果表-放弃项目")
    @RequiresPermissions("station:customer_reg_item_result:abandon")
    @PostMapping(value = "/abandon")
    public Result<?> abandon(@RequestBody CustomerRegItemResult customerRegItemResult) {
        if (customerRegDepartSummaryService.isSummaryAudited(customerRegItemResult.getCustomerRegId())) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.abandonItemResult(customerRegItemResult);
        return Result.OK("操作成功！");
    }

    /**
     * 反放弃组合
     *
     * @return
     */
    @AutoLog(value = "体检小项结果表-反放弃组合")
    @ApiOperation(value = "体检小项结果表-反放弃组合", notes = "体检小项结果表-反放弃组合")
    @RequiresPermissions("station:customer_reg_item_result:abandon")
    @GetMapping(value = "/unAbandonGroup")
    public Result<?> unAbandonGroup(String groupId, String regId) {
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.unAbandonGroup(groupId, regId);
        return Result.OK("保存成功！");
    }


    /**
     * 放弃组合
     *
     * @return
     */
    @AutoLog(value = "体检小项结果表-放弃组合")
    @ApiOperation(value = "体检小项结果表-放弃组合", notes = "体检小项结果表-放弃组合")
    @RequiresPermissions("station:customer_reg_item_result:abandon")
    @GetMapping(value = "/abandonGroup")
    public Result<?> abandonGroup(String groupId, String regId) {
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }

        customerRegItemResultService.abandonGroup(groupId, regId);
        return Result.OK("操作成功！");
    }

    /**
     * 放弃组合
     *
     * @return
     */
    @ApiOperation(value = "体检小项结果表-获取科室", notes = "体检小项结果表-获取科室")
    @GetMapping(value = "/getDepart")
    public Result<?> getDepart(String id) {

        return Result.OK(sysDepartService.getById(id));
    }


    /**
     * 放弃组合
     *
     * @return
     */
    @AutoLog(value = "体检小项结果表-清除组合结果")
    @ApiOperation(value = "体检小项结果表-清除组合结果", notes = "体检小项结果表-清除组合结果")
    @RequiresPermissions("station:customer_reg_item_result:abandon")
    @GetMapping(value = "/clearGroupResult")
    public Result<?> clearGroupResult(String groupId, String regId, String departmentId) {
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.clearGroupResult(groupId, regId, departmentId);
        return Result.OK("操作成功！");
    }

    /**
     * 放弃组合
     *
     * @return
     */
    @AutoLog(value = "体检小项结果表-放弃组合")
    @ApiOperation(value = "体检小项结果表-放弃组合", notes = "体检小项结果表-放弃组合")
    @RequiresPermissions("station:customer_reg_item_result:abandon")
    @PostMapping(value = "/abandonGroupBatch")
    public Result<?> abandonGroupBatch(@RequestBody JSONObject info) {

        List<String> groupId = info.getJSONArray("groupIds").toJavaList(String.class);
        String regId = info.getString("customerRegId");
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.abandonGroupBatch(regId, groupId);
        return Result.OK("操作成功！");
    }

    /**
     * 取消放弃组合
     *
     * @return
     */
    @AutoLog(value = "体检小项结果表-取消放弃组合")
    @ApiOperation(value = "体检小项结果表-取消放弃组合", notes = "体检小项结果表-取消放弃组合")
    @RequiresPermissions("station:customer_reg_item_result:abandon")
    @PostMapping(value = "/cancleAbandonGroupBatch")
    public Result<?> cancleAbandonGroupBatch(@RequestBody JSONObject info) {

        List<String> groupId = info.getJSONArray("groupIds").toJavaList(String.class);
        String regId = info.getString("customerRegId");
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.unAbandonGroupBatch(regId, groupId);
        return Result.OK("操作成功！");
    }

    //resetCheckStatusBatch
    @AutoLog(value = "体检小项结果表-重置检查状态")
    @ApiOperation(value = "体检小项结果表-重置检查状态", notes = "体检小项结果表-重置检查状态")
    @RequiresPermissions("station:customer_reg_item_result:resetCheckStatus")
    @PostMapping(value = "/resetCheckStatusBatch")
    public Result<?> resetCheckStatusBatch(@RequestBody JSONObject info) {
        List<String> groupId = info.getJSONArray("groupIds").toJavaList(String.class);
        String regId = info.getString("customerRegId");
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.resetCheckStatusBatch(regId, groupId);
        return Result.OK("操作成功！");
    }

    //unResetCheckStatusBatch
    @AutoLog(value = "体检小项结果表-标记为已检")
    @ApiOperation(value = "体检小项结果表-标记为已检", notes = "体检小项结果表-标记为已检")
    @RequiresPermissions("station:customer_reg_item_result:resetCheckStatus")
    @PostMapping(value = "/setStatusCheckedtusBatch")
    public Result<?> setStatusCheckedtusBatch(@RequestBody JSONObject info) {
        List<String> groupId = info.getJSONArray("groupIds").toJavaList(String.class);
        String regId = info.getString("customerRegId");
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }
        customerRegItemResultService.setStatusCheckedtusBatch(regId, groupId);
        return Result.OK("操作成功！");
    }

    //sendCheckNotifyBatch
    @AutoLog(value = "体检小项结果表-发送检查通知")
    @ApiOperation(value = "体检小项结果表-发送检查通知", notes = "体检小项结果表-发送检查通知")
    @RequiresPermissions("station:customer_reg_item_result:sendCheckNotify")
    @PostMapping(value = "/sendCheckNotifyBatch")
    public Result<?> sendCheckNotifyBatch(@RequestBody JSONObject info) {
        List<String> groupNames = info.getJSONArray("groupNames").toJavaList(String.class);
        String regId = info.getString("customerRegId");
        customerRegItemResultService.sendCheckNotifyBatch(regId, groupNames);
        return Result.OK("发送成功！");
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检小项结果表-通过id查询")
    @ApiOperation(value = "体检小项结果表-通过id查询", notes = "体检小项结果表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegItemResult> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegItemResult customerRegItemResult = customerRegItemResultService.getById(id);
        if (customerRegItemResult == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegItemResult);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegItemResult
     */
    @RequiresPermissions("station:customer_reg_item_result:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegItemResult customerRegItemResult) {
        return super.exportXls(request, customerRegItemResult, CustomerRegItemResult.class, "体检小项结果表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("station:customer_reg_item_result:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegItemResult.class);
    }

}
