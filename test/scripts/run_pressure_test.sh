#!/bin/bash

# WebDriverPool压力测试执行脚本
# 自动化执行压力测试并收集结果

# 配置参数
JMETER_HOME="/opt/apache-jmeter"  # JMeter安装路径，需要根据实际情况调整
TEST_PLAN="./jmeter/webdriver-pool-test.jmx"
RESULTS_DIR="./results"
LOGS_DIR="./logs"
REPORTS_DIR="./reports"

# 测试配置
SERVER_HOST="localhost"
SERVER_PORT="8080"
CONTEXT_PATH="/jeecg-boot"

# 创建必要的目录
mkdir -p $RESULTS_DIR $LOGS_DIR $REPORTS_DIR

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_warn() {
    echo -e "\033[33m[WARN]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 检查环境
check_environment() {
    print_info "检查测试环境..."
    
    # 检查JMeter
    if [ ! -d "$JMETER_HOME" ]; then
        print_error "JMeter未找到: $JMETER_HOME"
        print_info "请安装JMeter并设置正确的JMETER_HOME路径"
        exit 1
    fi
    
    # 检查测试计划文件
    if [ ! -f "$TEST_PLAN" ]; then
        print_error "测试计划文件未找到: $TEST_PLAN"
        exit 1
    fi
    
    # 检查服务器连接
    print_info "检查服务器连接: http://$SERVER_HOST:$SERVER_PORT$CONTEXT_PATH"
    if ! curl -s --connect-timeout 10 "http://$SERVER_HOST:$SERVER_PORT$CONTEXT_PATH/actuator/health" > /dev/null; then
        print_warn "无法连接到服务器，请确保应用已启动"
        read -p "是否继续执行测试? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_info "服务器连接正常"
    fi
    
    print_info "环境检查完成"
}

# 启动系统监控
start_monitoring() {
    print_info "启动系统监控..."
    
    # 启动系统监控脚本
    if [ -f "./scripts/monitor_system.sh" ]; then
        chmod +x ./scripts/monitor_system.sh
        ./scripts/monitor_system.sh &
        MONITOR_PID=$!
        echo $MONITOR_PID > $LOGS_DIR/monitor.pid
        print_info "系统监控已启动 (PID: $MONITOR_PID)"
    else
        print_warn "系统监控脚本未找到，跳过监控"
    fi
}

# 停止系统监控
stop_monitoring() {
    if [ -f "$LOGS_DIR/monitor.pid" ]; then
        local monitor_pid=$(cat $LOGS_DIR/monitor.pid)
        if kill -0 $monitor_pid 2>/dev/null; then
            print_info "停止系统监控 (PID: $monitor_pid)"
            kill -TERM $monitor_pid
            sleep 3
            if kill -0 $monitor_pid 2>/dev/null; then
                kill -KILL $monitor_pid
            fi
        fi
        rm -f $LOGS_DIR/monitor.pid
    fi
}

# 执行测试场景
run_test_scenario() {
    local scenario_name=$1
    local thread_group=$2
    local duration=$3
    
    print_info "执行测试场景: $scenario_name"
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local result_file="$RESULTS_DIR/${scenario_name}_${timestamp}.jtl"
    local log_file="$LOGS_DIR/${scenario_name}_${timestamp}.log"
    
    # JMeter命令参数
    local jmeter_cmd="$JMETER_HOME/bin/jmeter -n -t $TEST_PLAN"
    jmeter_cmd="$jmeter_cmd -l $result_file"
    jmeter_cmd="$jmeter_cmd -j $log_file"
    jmeter_cmd="$jmeter_cmd -JSERVER_HOST=$SERVER_HOST"
    jmeter_cmd="$jmeter_cmd -JSERVER_PORT=$SERVER_PORT"
    jmeter_cmd="$jmeter_cmd -JCONTEXT_PATH=$CONTEXT_PATH"
    
    # 启用特定的线程组
    if [ ! -z "$thread_group" ]; then
        jmeter_cmd="$jmeter_cmd -JthreadGroup=$thread_group"
    fi
    
    print_info "开始执行测试，预计持续时间: $duration"
    print_info "结果文件: $result_file"
    print_info "日志文件: $log_file"
    
    # 执行测试
    eval $jmeter_cmd
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_info "测试场景 '$scenario_name' 执行完成"
        
        # 生成HTML报告
        generate_html_report "$result_file" "$scenario_name" "$timestamp"
    else
        print_error "测试场景 '$scenario_name' 执行失败 (退出码: $exit_code)"
    fi
    
    return $exit_code
}

# 生成HTML报告
generate_html_report() {
    local result_file=$1
    local scenario_name=$2
    local timestamp=$3
    
    local report_dir="$REPORTS_DIR/${scenario_name}_${timestamp}"
    
    print_info "生成HTML报告: $report_dir"
    
    $JMETER_HOME/bin/jmeter -g "$result_file" -o "$report_dir"
    
    if [ $? -eq 0 ]; then
        print_info "HTML报告生成完成: $report_dir/index.html"
    else
        print_error "HTML报告生成失败"
    fi
}

# 分析测试结果
analyze_results() {
    print_info "分析测试结果..."
    
    local analysis_file="$REPORTS_DIR/test_analysis_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "WebDriverPool压力测试结果分析"
        echo "分析时间: $(date)"
        echo "=========================================="
        echo ""
        
        echo "1. 测试文件统计:"
        find $RESULTS_DIR -name "*.jtl" -exec echo "  {}" \; -exec wc -l {} \;
        echo ""
        
        echo "2. 错误统计:"
        for file in $RESULTS_DIR/*.jtl; do
            if [ -f "$file" ]; then
                local total=$(wc -l < "$file")
                local errors=$(grep -c "false" "$file" 2>/dev/null || echo 0)
                local success_rate=$(echo "scale=2; ($total - $errors) * 100 / $total" | bc -l 2>/dev/null || echo "N/A")
                echo "  $(basename $file): 总请求=$total, 错误=$errors, 成功率=${success_rate}%"
            fi
        done
        echo ""
        
        echo "3. 响应时间分析:"
        for file in $RESULTS_DIR/*.jtl; do
            if [ -f "$file" ]; then
                echo "  $(basename $file):"
                awk -F',' 'NR>1 {sum+=$2; count++; if($2>max) max=$2; if(min=="" || $2<min) min=$2} END {if(count>0) printf "    平均响应时间: %.2fms, 最小: %dms, 最大: %dms\n", sum/count, min, max}' "$file"
            fi
        done
        echo ""
        
        echo "4. 系统监控摘要:"
        if [ -f "$LOGS_DIR/monitor_report_"*.txt ]; then
            local latest_report=$(ls -t $LOGS_DIR/monitor_report_*.txt | head -1)
            echo "  最新监控报告: $(basename $latest_report)"
            tail -20 "$latest_report"
        else
            echo "  无监控报告"
        fi
        
    } > $analysis_file
    
    print_info "测试结果分析完成: $analysis_file"
    
    # 显示关键结果
    echo ""
    print_info "=== 测试结果摘要 ==="
    grep -E "(成功率|平均响应时间|Chrome进程总数)" $analysis_file | head -10
}

# 清理函数
cleanup() {
    print_info "清理测试环境..."
    stop_monitoring
    print_info "清理完成"
}

# 主函数
main() {
    print_info "WebDriverPool压力测试开始"
    print_info "测试时间: $(date)"
    
    # 设置信号处理
    trap cleanup SIGINT SIGTERM
    
    # 检查环境
    check_environment
    
    # 启动监控
    start_monitoring
    
    # 等待监控启动
    sleep 5
    
    # 执行测试场景
    local overall_result=0
    
    # 场景1: 正常负载测试
    print_info "开始场景1: 正常负载测试"
    run_test_scenario "normal_load" "场景1-正常负载测试" "30分钟"
    if [ $? -ne 0 ]; then
        overall_result=1
    fi
    
    # 等待间隔
    print_info "等待5分钟后开始下一个场景..."
    sleep 300
    
    # 场景2: 高并发冲击测试
    print_info "开始场景2: 高并发冲击测试"
    run_test_scenario "high_concurrency" "场景2-高并发冲击测试" "10分钟"
    if [ $? -ne 0 ]; then
        overall_result=1
    fi
    
    # 询问是否执行长时间测试
    echo ""
    read -p "是否执行长时间稳定性测试 (4小时)? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "开始场景3: 长时间稳定性测试"
        run_test_scenario "stability" "场景3-长时间稳定性测试" "4小时"
        if [ $? -ne 0 ]; then
            overall_result=1
        fi
    fi
    
    # 停止监控
    stop_monitoring
    
    # 分析结果
    analyze_results
    
    # 测试完成
    if [ $overall_result -eq 0 ]; then
        print_info "所有测试场景执行完成"
    else
        print_warn "部分测试场景执行失败，请检查日志"
    fi
    
    print_info "测试结果位于: $RESULTS_DIR"
    print_info "测试报告位于: $REPORTS_DIR"
    print_info "测试日志位于: $LOGS_DIR"
    
    return $overall_result
}

# 显示帮助信息
show_help() {
    echo "WebDriverPool压力测试执行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -s, --scenario SCENARIO  执行特定场景 (normal_load|high_concurrency|stability)"
    echo "  --host HOST    服务器地址 (默认: localhost)"
    echo "  --port PORT    服务器端口 (默认: 8080)"
    echo "  --context PATH 上下文路径 (默认: /jeecg-boot)"
    echo ""
    echo "示例:"
    echo "  $0                           # 执行所有测试场景"
    echo "  $0 -s normal_load           # 只执行正常负载测试"
    echo "  $0 --host *************     # 指定服务器地址"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--scenario)
            SCENARIO="$2"
            shift 2
            ;;
        --host)
            SERVER_HOST="$2"
            shift 2
            ;;
        --port)
            SERVER_PORT="$2"
            shift 2
            ;;
        --context)
            CONTEXT_PATH="$2"
            shift 2
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
if [ ! -z "$SCENARIO" ]; then
    # 执行特定场景
    check_environment
    start_monitoring
    sleep 5
    
    case $SCENARIO in
        normal_load)
            run_test_scenario "normal_load" "场景1-正常负载测试" "30分钟"
            ;;
        high_concurrency)
            run_test_scenario "high_concurrency" "场景2-高并发冲击测试" "10分钟"
            ;;
        stability)
            run_test_scenario "stability" "场景3-长时间稳定性测试" "4小时"
            ;;
        *)
            print_error "未知场景: $SCENARIO"
            exit 1
            ;;
    esac
    
    stop_monitoring
    analyze_results
else
    # 执行所有场景
    main
fi
