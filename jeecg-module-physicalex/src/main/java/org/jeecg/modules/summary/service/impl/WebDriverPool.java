package org.jeecg.modules.summary.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.logging.LoggingPreferences;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.logging.Level;

public class WebDriverPool {
    private static final Logger log = LoggerFactory.getLogger(WebDriverPool.class);
    private final BlockingQueue<WebDriver> pool;
    private final int maxPoolSize;
    private final String chromeDriverPath;
    private final String chromePath;
    private final Map<WebDriver, Integer> usageCounter = new ConcurrentHashMap<>();
    private final int MAX_USAGE_COUNT = 15; // 最多使用50次后重新创建

    public WebDriverPool(int maxPoolSize, String chromeDriverPath, String chromePath) {
        this.maxPoolSize = maxPoolSize;
        this.pool = new LinkedBlockingQueue<>(maxPoolSize);
        this.chromeDriverPath = chromeDriverPath;
        this.chromePath = chromePath;

        for (int i = 0; i < maxPoolSize; i++) {
            pool.add(createWebDriver(chromeDriverPath, chromePath));
        }
    }

    private WebDriver createWebDriver(String chromeDriverPath, String chromePath) {
        System.setProperty("webdriver.chrome.driver", chromeDriverPath);
        LoggingPreferences logPrefs = new LoggingPreferences();
        // Capture BROWSER logs (console.log, console.error, etc.)
        // Set Level.ALL to capture everything (INFO, WARNING, SEVERE, etc.)
        logPrefs.enable(LogType.BROWSER, Level.ALL);
        logPrefs.enable(LogType.PERFORMANCE, Level.ALL);

        ChromeOptions options = new ChromeOptions();
        options.setCapability("goog:loggingPrefs", logPrefs);
        //options.addArguments("--headless=new");  // 使用新的无头模式
        //options.addArguments("--disable-gpu");
        //options.addArguments("--no-sandbox");
        //options.addArguments("--remote-allow-origins=*");
        //options.addArguments("--js-flags=--max-old-space-size=512");

        // 基础无头模式配置
        options.addArguments("--headless=new");
        options.addArguments("--disable-gpu");
        options.addArguments("--no-sandbox");
        options.addArguments("--remote-allow-origins=*");

        // 关键内存优化参数
        options.addArguments("--js-flags=--max-old-space-size=1024");  // 增加到2GB
        options.addArguments("--disable-dev-shm-usage");              // 禁用/dev/shm使用
        options.addArguments("--memory-pressure-off");                // 关闭内存压力检测

        // 禁用各种缓存和功能以节省内存
        options.addArguments("--disable-cache");
        options.addArguments("--disable-application-cache");
        options.addArguments("--disable-offline-load-stale-cache");
        options.addArguments("--disk-cache-size=0");
        options.addArguments("--media-cache-size=0");

        // 禁用扩展和通知
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-infobars");
        options.addArguments("--disable-notifications");
        options.addArguments("--disable-background-timer-throttling");
        options.addArguments("--disable-backgrounding-occluded-windows");
        options.addArguments("--disable-renderer-backgrounding");

        // 性能优化参数
        options.addArguments("--disable-features=TranslateUI");
        options.addArguments("--disable-features=IsolateOrigins");
        options.addArguments("--disable-site-isolation-trials");
        options.addArguments("--disable-features=VizDisplayCompositor");

        // 禁用自动化控制条
        options.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"});
        options.setExperimentalOption("useAutomationExtension", false);
        // 设置 Chrome 二进制文件路径
        if (chromePath != null && !chromePath.isEmpty()) {
            options.setBinary(chromePath);
        }

        log.info("创建新的Chrome实例");
        ChromeDriver driver = new ChromeDriver(options);

        // 设置超时时间
        driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(60));
        driver.manage().timeouts().scriptTimeout(Duration.ofSeconds(60));
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));

        return driver;
    }

    public WebDriver borrowWebDriver(String url) throws InterruptedException {
        while (true) {
            WebDriver driver = pool.take();
            try {
                // 全面的健康检查
                if (!isDriverHealthy(driver)) {
                    log.warn("检测到不健康的Chrome实例，重新创建");
                    recreateDriver(driver);
                    continue; // 重新从池中获取
                }
                // 增强健康检查：验证当前URL是否符合预期
                driver.getWindowHandle();
                String currentUrl = driver.getCurrentUrl();

                // 检查使用次数
                Integer count = usageCounter.getOrDefault(driver, 0);
                if (count >= MAX_USAGE_COUNT) {
                    // 超过最大使用次数，销毁并重新创建
                    log.info("重新创建Chrome实例，已达到最大使用次数: {}", count);
                    driver.quit();
                    usageCounter.remove(driver);
                    WebDriver newDriver = createWebDriver(chromeDriverPath, chromePath);
                    usageCounter.put(newDriver, 1);
                    driver = newDriver;
                } else {
                    usageCounter.put(driver, count + 1);
                    log.debug("使用Chrome实例，当前使用次数: {}", count + 1);
                }

                // 新增初始化导航
                if (!StringUtils.equals(currentUrl, url)) {
                    log.info("导航到URL: {}", url);
                    driver.get(url);

                    // 等待页面加载完成
                    JavascriptExecutor js = (JavascriptExecutor) driver;
                    long startTime = System.currentTimeMillis();
                    while (true) {
                        try {
                            Boolean isPageLoaded = (Boolean) js.executeScript("return document.readyState === 'complete'");
                            if (Boolean.TRUE.equals(isPageLoaded)) {
                                break;
                            }

                            if (System.currentTimeMillis() - startTime > 30000) {
                                log.warn("页面加载超时，继续执行");
                                break;
                            }

                            Thread.sleep(500);
                        } catch (Exception e) {
                            log.warn("检查页面加载状态时出错: {}", e.getMessage());
                            break;
                        }
                    }
                }

                return driver;
            } catch (Exception e) {
                // 异常处理中创建新实例时使用原始配置参数
                log.warn("检测到Chrome实例异常，重新创建", e);
                try {
                    driver.quit();
                } catch (Exception ex) {
                    // 忽略关闭异常
                }
                usageCounter.remove(driver);
                synchronized (this) {
                    if (pool.size() < maxPoolSize) {
                        WebDriver newDriver = createWebDriver(chromeDriverPath, chromePath);
                        usageCounter.put(newDriver, 0);
                        pool.add(newDriver);
                    }
                }
            }
        }
    }

    public void returnWebDriver(WebDriver driver) {
        try {
            // 再次检查驱动是否可用
            driver.getWindowHandle();

            // 强制清理浏览器资源
            driver.manage().deleteAllCookies();

            // 清理本地存储和会话存储
            if (driver instanceof JavascriptExecutor) {
                JavascriptExecutor js = (JavascriptExecutor) driver;
                try {
                    // 清理存储
                    js.executeScript("localStorage.clear();");
                    js.executeScript("sessionStorage.clear();");

                    // 清理内存中的大对象
                    js.executeScript("window.reportData = null; window.templateData = null;");

                    // 强制垃圾回收
                    js.executeScript("if (window.gc) { window.gc(); }");

                    // 记录内存使用情况
                    js.executeScript("if (window.performance && window.performance.memory) {" + "  console.log('Memory usage - used:', window.performance.memory.usedJSHeapSize, " + "  'total:', window.performance.memory.totalJSHeapSize, " + "  'limit:', window.performance.memory.jsHeapSizeLimit);" + "}");
                } catch (Exception ex) {
                    log.warn("执行内存清理失败", ex);
                }
            }

            pool.offer(driver);
        } catch (Exception e) {
            // 如果不可用则销毁
            log.warn("检测到Chrome实例异常，销毁实例", e);
            try {
                driver.quit();
            } catch (Exception ex) {
                // 忽略关闭异常
            }
            usageCounter.remove(driver);

            // 创建新实例替换
            synchronized (this) {
                if (pool.size() < maxPoolSize) {
                    try {
                        WebDriver newDriver = createWebDriver(chromeDriverPath, chromePath);
                        usageCounter.put(newDriver, 0);
                        pool.add(newDriver);
                        log.info("已创建新的Chrome实例替换异常实例");
                    } catch (Exception ex) {
                        log.error("创建替换Chrome实例失败", ex);
                    }
                }
            }
        }
    }

    public void shutdown() {
        log.info("关闭WebDriver池，清理所有Chrome实例");
        for (WebDriver driver : pool) {
            try {
                driver.quit();
            } catch (Exception e) {
                log.warn("关闭Chrome实例异常", e);
            }
        }
        pool.clear();
        usageCounter.clear();
    }
    /**
     * 全面检查WebDriver是否健康可用
     */
    private boolean isDriverHealthy(WebDriver driver) {
        if (driver == null) {
            return false;
        }

        try {
            // 1. 检查窗口句柄（最基本的检查）
            driver.getWindowHandle();

            // 2. 检查会话ID是否有效
            String sessionId = ((ChromeDriver) driver).getSessionId().toString();
            if (StringUtils.isEmpty(sessionId)) {
                return false;
            }

            // 3. 尝试执行简单的JavaScript（检查渲染引擎）
            JavascriptExecutor js = (JavascriptExecutor) driver;
            Object result = js.executeScript("return 'test'");
            if (!"test".equals(result)) {
                return false;
            }

            // 4. 检查页面标题（确保页面可访问）
            driver.getTitle();

            return true;

        } catch (org.openqa.selenium.NoSuchSessionException e) {
            log.warn("WebDriver会话已失效: {}", e.getMessage());
            return false;
        } catch (org.openqa.selenium.WebDriverException e) {
            log.warn("WebDriver连接异常: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.warn("WebDriver健康检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 重新创建WebDriver实例
     */
    private void recreateDriver(WebDriver oldDriver) {
        // 安全关闭旧实例
        try {
            oldDriver.quit();
        } catch (Exception e) {
            log.debug("关闭异常WebDriver实例时出错（忽略）: {}", e.getMessage());
        }
        usageCounter.remove(oldDriver);

        // 创建新实例并放入池中
        synchronized (this) {
            try {
                WebDriver newDriver = createWebDriver(chromeDriverPath, chromePath);
                usageCounter.put(newDriver, 0);
                pool.offer(newDriver);
                log.info("成功创建新的Chrome实例替换异常实例");
            } catch (Exception e) {
                log.error("创建替换Chrome实例失败", e);
                // 如果创建失败，确保池中至少有一个可用实例
                if (pool.isEmpty()) {
                    throw new RuntimeException("无法创建WebDriver实例，池已空", e);
                }
            }
        }
    }

    /**
     * 安全导航到指定URL
     */
    private void navigateToUrl(WebDriver driver, String url) {
        try {
            String currentUrl = driver.getCurrentUrl();
            if (!StringUtils.equals(currentUrl, url)) {
                log.info("导航到URL: {}", url);
                driver.get(url);

                // 等待页面加载完成
                JavascriptExecutor js = (JavascriptExecutor) driver;
                long startTime = System.currentTimeMillis();
                while (System.currentTimeMillis() - startTime < 30000) {
                    try {
                        Boolean isPageLoaded = (Boolean) js.executeScript("return document.readyState === 'complete'");
                        if (Boolean.TRUE.equals(isPageLoaded)) {
                            break;
                        }
                        Thread.sleep(500);
                    } catch (Exception e) {
                        log.warn("检查页面加载状态时出错: {}", e.getMessage());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("导航到URL失败: {}", e.getMessage());
            throw new RuntimeException("页面导航失败", e);
        }
    }
}