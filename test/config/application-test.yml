# WebDriverPool压力测试环境配置
# 基于application-dev.yml进行测试优化

server:
  port: 8080
  servlet:
    context-path: /jeecg-boot

# WebDriver配置
selenium:
  chromeDriverPath: /usr/local/bin/chromedriver  # 根据实际路径调整
  chromePath: /usr/bin/google-chrome             # 根据实际路径调整

# WebDriver池配置
webdriver:
  pool:
    size: 3  # 测试环境适当增加池大小
  
  # 监控配置 - 更频繁的监控用于测试
  monitor:
    enabled: true
    interval.minutes: 1  # 每分钟检查一次
    max.processes: 15    # 测试环境允许更多进程
    cleanup.threshold: 20 # 更高的清理阈值
  
  # 并发控制配置 - 测试环境优化
  concurrency:
    max.concurrent: 5      # 允许更多并发
    queue.size: 20         # 更大的队列
    timeout.seconds: 120   # 更长的超时时间
    retry.max: 3
    retry.delay.ms: 1000

# 日志配置 - 详细日志用于测试分析
logging:
  level:
    root: INFO
    org.jeecg.modules.summary.service.impl: DEBUG
    org.jeecg.modules.station.service.impl: DEBUG
    org.jeecg.modules.summary.service.impl.WebDriverPoolManager: DEBUG
    org.jeecg.modules.summary.service.impl.WebDriverProcessMonitor: DEBUG
    org.jeecg.modules.summary.service.impl.WebDriverConcurrencyController: DEBUG
    org.jeecg.modules.summary.service.impl.WebDriverExceptionHandler: DEBUG
  
  # 日志文件配置
  file:
    name: logs/webdriver-pool-test.log
    max-size: 100MB
    max-history: 10
  
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 数据库配置 - 测试环境
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ******************************************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
          
  # Redis配置 - 测试环境
  redis:
    host: localhost
    port: 6379
    password: 
    database: 1  # 使用不同的数据库避免冲突
    timeout: 6000ms
    jedis:
      pool:
        max-active: 1000
        max-wait: -1ms
        max-idle: 10
        min-idle: 5

# JVM参数建议（在启动脚本中设置）
# -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
# -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:logs/gc.log

# 系统监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 测试专用配置
test:
  # PDF生成测试配置
  pdf:
    # 测试用的报告生成URL
    reportGenerateUrl: http://localhost:8080/jeecg-boot/summary/report/generate
    # 本地文件服务器地址
    localFileServerDomain: http://localhost:8080/jeecg-boot
    
  # 测试数据配置
  data:
    # 测试用的客户登记ID范围
    customerRegIdStart: reg001
    customerRegIdEnd: reg050
    
  # 性能测试阈值
  performance:
    # 响应时间阈值（毫秒）
    responseTimeThreshold: 30000
    # 成功率阈值（百分比）
    successRateThreshold: 95
    # Chrome进程数量阈值
    chromeProcessThreshold: 10
