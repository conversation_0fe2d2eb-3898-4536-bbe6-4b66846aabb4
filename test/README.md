# WebDriverPool压力测试套件

这是一个完整的WebDriverPool压力测试套件，用于验证WebDriverPool优化方案的效果。

## 目录结构

```
test/
├── README.md                    # 本文件
├── config/
│   └── application-test.yml     # 测试环境配置
├── data/
│   └── customer_reg_ids.csv     # 测试数据
├── jmeter/
│   └── webdriver-pool-test.jmx  # JMeter测试计划
├── scripts/
│   ├── setup_test_environment.sh    # 环境设置脚本
│   ├── run_pressure_test.sh         # 测试执行脚本
│   ├── monitor_system.sh            # 系统监控脚本
│   └── analyze_test_results.py      # 结果分析脚本
├── results/                     # 测试结果目录（自动创建）
├── logs/                        # 日志目录（自动创建）
└── reports/                     # 报告目录（自动创建）
```

## 快速开始

### 1. 环境准备

```bash
# 进入测试目录
cd test

# 设置测试环境（自动安装JMeter、Chrome等）
chmod +x scripts/setup_test_environment.sh
./scripts/setup_test_environment.sh
```

### 2. 启动应用

```bash
# 使用测试配置启动应用
java -jar -Dspring.profiles.active=test \
     -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     ../target/jeecg-boot-module-system-2.4.6.jar
```

### 3. 执行测试

```bash
# 执行完整测试套件
./scripts/run_pressure_test.sh

# 或执行特定场景
./scripts/run_pressure_test.sh -s normal_load      # 正常负载测试
./scripts/run_pressure_test.sh -s high_concurrency # 高并发测试
./scripts/run_pressure_test.sh -s stability       # 稳定性测试
```

### 4. 分析结果

```bash
# 安装Python依赖（如果需要）
pip install matplotlib pandas

# 分析测试结果
python scripts/analyze_test_results.py

# 查看报告
open reports/performance_report_*.html
```

## 测试场景

### 场景1: 正常负载测试
- **目标**: 验证系统在正常负载下的稳定性
- **并发用户**: 15个
- **持续时间**: 30分钟
- **请求间隔**: 2-5秒随机

### 场景2: 高并发冲击测试
- **目标**: 测试系统在高并发下的表现
- **并发用户**: 50个
- **持续时间**: 10分钟
- **请求间隔**: 1秒固定

### 场景3: 长时间稳定性测试
- **目标**: 验证长时间运行的稳定性
- **并发用户**: 10个
- **持续时间**: 4小时
- **请求间隔**: 3秒固定

## 监控指标

### 系统级指标
- CPU使用率
- 内存使用率
- 磁盘I/O
- 网络I/O

### 应用级指标
- Chrome进程数量
- WebDriver池状态
- 并发请求数
- JVM内存使用

### 业务级指标
- PDF生成成功率
- 请求吞吐量(TPS)
- 响应时间分布
- 错误率

## 成功标准

### 性能指标
- **响应时间**: 95%请求 < 30秒
- **吞吐量**: > 2 TPS
- **成功率**: > 95%
- **Chrome进程**: ≤ 10个

### 稳定性指标
- **内存泄漏**: 4小时测试内存增长 < 20%
- **进程泄漏**: 无僵尸Chrome进程
- **错误恢复**: 异常后30秒内恢复正常

## 配置说明

### JMeter配置
- 测试计划文件: `jmeter/webdriver-pool-test.jmx`
- 可通过JMeter GUI打开和修改
- 支持命令行参数覆盖配置

### 测试数据
- 客户登记ID: `data/customer_reg_ids.csv`
- 包含50个测试用的客户记录
- 可根据需要扩展测试数据

### 应用配置
- 测试配置文件: `config/application-test.yml`
- 针对测试环境优化的参数
- 包含详细的日志配置

## 故障排除

### 常见问题

1. **Chrome进程过多**
   ```bash
   # 手动清理Chrome进程
   pkill -f chrome
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   # 调整JVM参数
   ```

3. **测试失败**
   ```bash
   # 检查应用日志
   tail -f logs/webdriver-pool-test.log
   # 检查JMeter日志
   tail -f logs/*_test.log
   ```

### 日志位置
- 应用日志: `logs/webdriver-pool-test.log`
- 系统监控日志: `logs/system_monitor_*.log`
- JMeter日志: `logs/*_test.log`
- 测试结果: `results/*.jtl`

## 自定义测试

### 修改测试参数
1. 编辑JMeter测试计划文件
2. 调整线程组配置
3. 修改请求参数

### 添加新的测试场景
1. 在JMeter中创建新的线程组
2. 配置相应的HTTP请求
3. 更新测试脚本

### 扩展监控指标
1. 修改监控脚本
2. 添加新的指标收集
3. 更新分析脚本

## 最佳实践

1. **测试前准备**
   - 确保测试环境资源充足
   - 清理之前的测试数据
   - 验证应用正常运行

2. **测试执行**
   - 逐步增加负载
   - 监控系统资源使用
   - 及时记录异常情况

3. **结果分析**
   - 对比基线数据
   - 关注趋势变化
   - 识别性能瓶颈

4. **报告生成**
   - 包含完整的测试数据
   - 提供明确的结论
   - 给出优化建议

## 联系支持

如果在使用过程中遇到问题，请：
1. 检查日志文件
2. 参考故障排除指南
3. 查看相关文档

---

**注意**: 这个测试套件专门为WebDriverPool优化方案设计，请确保在合适的测试环境中运行，避免对生产环境造成影响。
