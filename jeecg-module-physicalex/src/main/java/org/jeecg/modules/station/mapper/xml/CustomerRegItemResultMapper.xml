<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.station.mapper.CustomerRegItemResultMapper">

    <select id="listCustomerRegGroupByRegId" resultMap="org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper.customerRegItemGroupMap"
            parameterType="java.lang.String">
        select g.*
        from customer_reg_item_group g join sys_depart d on g.department_id= d.id join item_group ig on g.item_group_id=ig.id
        <where> g.customer_reg_id = #{regId}
        <if test="!containFeeOnly">
        and ig.charge_item_only_flag != 1
        </if>
        <if test="departmentIds!=null and departmentIds.size()>0">
            and g.department_id in
            <foreach collection="departmentIds" item="departmentId" open="(" close=")" separator=",">
                #{departmentId}
            </foreach>
        </if>
        </where>
        order by d.depart_order,g.department_id
    </select>

    <select id="listItemByCustomerGroup" resultMap="itemInfoResultMap">
        select i.*,
               igi.seq,
               r.id                 as r_id,
               r.create_by          as r_create_by,
               r.create_time        as r_create_time,
               r.update_by          as r_update_by,
               r.update_time        as r_update_time,
               r.sys_org_code       as r_sys_org_code,
               r.customer_reg_id    as r_customer_reg_id,
               r.item_group_id      as r_item_group_id,
               r.item_group_name    as r_item_group_name,
               r.item_id            as r_item_id,
               r.item_name          as r_item_name,
               r.item_code          as r_item_code,
               r.value_type         as r_value_type,
               r.value              as r_value,
               r.value_source       as r_value_source,
               r.doctor_id          as r_doctor_id,
               r.doctor_name        as r_doctor_name,
               r.pic                as r_pic,
               r.instrument         as r_instrument,
               r.unit               as r_unit,
               r.value_indicator    as r_value_indicator,
               r.value_ref_range    as r_value_ref_range,
               r.check_conclusion   as r_check_conclusion,
               r.check_purpose      as r_check_purpose,
               r.check_parts        as r_check_parts,
               r.check_observations as r_check_observations,
               r.abandon_flag       as r_abandon_flag,
               r.abnormal_flag      as r_abnormal_flag,
               r.abnormal_symbol    as r_abnormal_symbol,
               r.abnormal_flag_desc as r_abnormal_flag_desc,
               r.critical_flag      as r_critical_flag,
               r.critical_degree    as r_critical_degree
        from item_info i
                 join itemgroup_item igi on i.id = igi.item_id
                 left join customer_reg_item_result r on i.id = r.item_id and r.customer_reg_id = #{regId}
        where igi.group_id = #{groupId}
--         and checkStatus !='未检'
          <if test="checkPartCode!=null and checkPartCode!=''">
          and (r.id is null or r.check_part_code = #{checkPartCode})
          </if>
          and i.del_flag = 0 order by igi.seq
    </select>
    <select id="listByRegIdAndDepartId" resultType="org.jeecg.modules.station.entity.CustomerRegItemResult">
        select * from customer_reg_item_result  where customer_reg_id = #{regId}
        <if test="departmentIds!=null">
            and department_id in
            <foreach collection="departmentIds" item="departmentId" open="(" close=")" separator=",">
                #{departmentId}
            </foreach>
        </if>
    </select>

    <select id="listByRegId" resultMap="itemResult">
        select c.*
        from customer_reg_item_result c
        where c.customer_reg_id = #{regId} order by sort_no
    </select>

    <select id="listHistoryResultByIdCard" resultType="org.jeecg.modules.station.entity.CustomerRegItemResult">
        select *,year(create_time) as checkYear,concat(value,'(',unit,')') as value_unit from customer_reg_item_result
        where id_card=#{idCard} and TIMESTAMPDIFF(YEAR, create_time, NOW()) &lt; #{years}
    </select>

    <select id="listKeyHistoryResultByIdCard" resultType="org.jeecg.modules.station.entity.CustomerRegItemResult">
        select *,year(create_time) as checkYear,concat(value,'(',unit,')') as value_unit from customer_reg_item_result
        where id_card=#{idCard} and abnormal_flag='1' and TIMESTAMPDIFF(YEAR, create_time, NOW()) &lt; #{years}
    </select>
    <select id="listItemResultByReg" resultType="org.jeecg.modules.station.entity.CustomerRegItemResult" resultMap="ItemResultMap">
        select * from customer_reg_item_result where customer_reg_id=#{regId} order by sort_no
    </select>
    <select id="listAll" resultMap="ItemResultMap">
        select * from customer_reg_item_result
    </select>
    <select id="listHistoryAbnormaoResultIdsByIdCard" resultType="java.lang.String">
        select concat(item_name,':',value,'(',unit,')') as abnormalItem from customer_reg_item_result
        where id_card=#{idCard} and TIMESTAMPDIFF(YEAR, create_time, NOW()) &lt; #{years} and abnormal_flag='1'
    </select>
    <select id="getItemResultByReg" resultType="org.jeecg.modules.station.entity.CustomerRegItemResult">
        select r.id,r.item_group_id,r.item_group_name,r.item_id,r.item_name,r.value_type,r.value,r.unit,r.value_low,r.value_high,r.value_ref_range,r.abnormal_flag,r.abnormal_flag_desc,r.check_conclusion,r.critical_flag,i.sumable_normalval_flag,i.sumable_flag from customer_reg_item_result r left join item_info i on r.item_id= i.id where customer_reg_id=#{customerRegId}
    </select>
    <select id="getByIdcard" resultType="org.jeecg.modules.station.entity.CustomerRegItemResult">
        select id,item_name,item_his_code,value,unit,value_ref_range,create_time,item_group_id,item_group_name,abnormal_flag,abnormal_flag_desc,value_type from customer_reg_item_result where id_card=#{idCard}
    </select>

    <resultMap id="ItemResultMap" type="org.jeecg.modules.station.entity.CustomerRegItemResult">
        <id property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sysOrgCode" column="sys_org_code"/>
        <result property="customerRegId" column="customer_reg_id"/>
        <result property="itemGroupId" column="item_group_id"/>
        <result property="itemGroupName" column="item_group_name"/>
        <result property="itemId" column="item_id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="valueType" column="value_type"/>
        <result property="value" column="value"/>
        <result property="valueSource" column="value_source"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="pic" column="pic"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="instrument" column="instrument"/>
        <result property="unit" column="unit"/>
        <result property="valueIndicator" column="value_indicator"/>
        <result property="valueRefRange" column="value_ref_range"/>
        <result property="checkConclusion" column="check_conclusion"/>
        <result property="checkPurpose" column="check_purpose"/>
        <result property="checkParts" column="check_parts"/>
        <result property="checkObservations" column="check_observations"/>
        <result property="abandonFlag" column="abandon_flag"/>
        <result property="abnormalFlag" column="abnormal_flag"/>
        <result property="abnormalSymbol" column="abnormal_symbol"/>
        <result property="abnormalFlagDesc" column="abnormal_flag_desc"/>
        <result property="criticalFlag" column="critical_flag"/>
        <result property="criticalDegree" column="critical_degree"/>
        <result property="departmentId" column="department_id"/>
        <result property="idCard" column="id_card"/>
        <result property="archivesNum" column="archives_num"/>
    </resultMap>

    <resultMap id="itemInfoResultMap" type="org.jeecg.modules.basicinfo.entity.ItemInfo">
        <id property="id" column="id"/>
        <result property="departmentId" column="department_id"/>
        <result property="sort" column="sort"/>
        <result property="seq" column="seq"/>
        <result property="name" column="name"/>
        <result property="helpChar" column="help_char"/>
        <result property="hisCode" column="his_code"/>
        <result property="hisName" column="his_name"/>
        <result property="wubiChar" column="wubi_char"/>
        <result property="sex" column="sex"/>
        <result property="minAge" column="min_age"/>
        <result property="maxAge" column="max_age"/>
        <result property="itemType" column="item_type"/>
        <result property="clinicalType" column="clinical_type"/>
        <result property="unit" column="unit"/>
        <result property="summaryFormat" column="summary_format"/>
        <result property="decimalPlaces" column="decimal_places"/>
        <result property="mutuallyExclusiveItem" column="mutually_exclusive_item"/>
        <result property="notice" column="notice"/>
        <result property="remark" column="remark"/>
        <result property="formula" column="formula"/>
        <result property="limitWord" column="limit_word"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="chargeOnlyFlag" column="charge_only_flag"/>
        <result property="printReportFlag" column="print_report_flag"/>
        <result property="complexDiagFlag" column="complex_diag_flag"/>
        <result property="picItemFlag" column="pic_item_flag"/>
        <result property="requiredFlag" column="required_flag"/>
        <result property="sumableFlag" column="sumable_flag"/>
        <result property="sumableNormalvalFlag" column="sumable_normalval_flag"/>
        <result property="sumableBoolFlag" column="sumable_bool_flag"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTy" column="create_ty"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="normalRef" column="normal_ref"/>
        <association property="itemResult" javaType="org.jeecg.modules.station.entity.CustomerRegItemResult"
                     columnPrefix="r_">
            <id property="id" column="id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="sysOrgCode" column="sys_org_code"/>
            <result property="customerRegId" column="customer_reg_id"/>
            <result property="itemGroupId" column="item_group_id"/>
            <result property="itemGroupName" column="item_group_name"/>
            <result property="itemId" column="item_id"/>
            <result property="itemName" column="item_name"/>
            <result property="itemCode" column="item_code"/>
            <result property="valueType" column="value_type"/>
            <result property="value" column="value"/>
            <result property="valueSource" column="value_source"/>
            <result property="doctorId" column="doctor_id"/>
            <result property="doctorName" column="doctor_name"/>
            <result property="pic" column="pic"
                    typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
            <result property="instrument" column="instrument"/>
            <result property="unit" column="unit"/>
            <result property="valueIndicator" column="value_indicator"/>
            <result property="valueRefRange" column="value_ref_range"/>
            <result property="checkConclusion" column="check_conclusion"/>
            <result property="checkPurpose" column="check_purpose"/>
            <result property="checkParts" column="check_parts"/>
            <result property="checkObservations" column="check_observations"/>
            <result property="abandonFlag" column="abandon_flag"/>
            <result property="abnormalFlag" column="abnormal_flag"/>
            <result property="abnormalSymbol" column="abnormal_symbol"/>
            <result property="abnormalFlagDesc" column="abnormal_flag_desc"/>
            <result property="criticalFlag" column="critical_flag"/>
            <result property="criticalDegree" column="critical_degree"/>
            <result property="departmentId" column="department_id"/>
            <result property="idCard" column="id_card"/>
            <result property="archivesNum" column="archives_num"/>
        </association>
    </resultMap>

    <resultMap id="itemResult" type="org.jeecg.modules.station.entity.CustomerRegItemResult">
        <id property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sysOrgCode" column="sys_org_code"/>
        <result property="customerRegId" column="customer_reg_id"/>
        <result property="itemGroupId" column="item_group_id"/>
        <result property="itemGroupName" column="item_group_name"/>
        <result property="itemId" column="item_id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="valueType" column="value_type"/>
        <result property="value" column="value"/>
        <result property="valueSource" column="value_source"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="pic" column="pic"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="instrument" column="instrument"/>
        <result property="unit" column="unit"/>
        <result property="valueIndicator" column="value_indicator"/>
        <result property="valueRefRange" column="value_ref_range"/>
        <result property="checkConclusion" column="check_conclusion"/>
        <result property="checkPurpose" column="check_purpose"/>
        <result property="checkParts" column="check_parts"/>
        <result property="checkObservations" column="check_observations"/>
        <result property="abandonFlag" column="abandon_flag"/>
        <result property="abnormalFlag" column="abnormal_flag"/>
        <result property="abnormalSymbol" column="abnormal_symbol"/>
        <result property="abnormalFlagDesc" column="abnormal_flag_desc"/>
        <result property="criticalFlag" column="critical_flag"/>
        <result property="criticalDegree" column="critical_degree"/>
    </resultMap>
</mapper>