<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper">

    <update id="updateItemGroupCheckStatusBatch">
        update customer_reg_item_group set check_status = #{status},check_time=now() ,check_doctor_code=#{checkDoctorCode},check_doctor_name=#{checkDoctorName} where customer_reg_id = #{regId} <if test="statusLimit!=null and statusLimit!=''"> and check_status=#{statusLimit}</if>  and item_group_id in <foreach collection="itemGroupIds" item="item" open="(" separator="," close=")">
            #{item} </foreach>
    </update>
    <update id="updatePayStatusByIds">
        update customer_reg_item_group set pay_status = #{status} where id in <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item} </foreach> and pay_status = #{statusLimit}
    </update>
    <update id="updateItemGroupAbnormalFlagBatch">
        update customer_reg_item_group set abnormal_flag = #{abnormalFlag} where customer_reg_id = #{regId} and item_group_id in <foreach collection="itemGroupIds" item="item" open="(" separator="," close=")">
            #{item} </foreach>
    </update>

    <!-- 批量更新项目组异常标志（支持部位） -->
    <update id="updateItemGroupAbnormalFlagBatchWithPart">
        update customer_reg_item_group set abnormal_flag = #{abnormalFlag}
        where customer_reg_id = #{regId}
        and (
        <foreach collection="itemGroupIds" item="itemGroupId" index="index" separator=" or ">
            (item_group_id = #{itemGroupId}
            <if test="checkPartCodes != null and checkPartCodes.size() > index">
                and (check_part_code = #{checkPartCodes[${index}]} or check_part_code is null)
            </if>)
        </foreach>
        )
    </update>

    <!-- 批量更新项目组检查状态（支持部位） -->
    <update id="updateItemGroupCheckStatusBatchWithPart">
        update customer_reg_item_group set check_status = #{status}, check_time = now(),
               check_doctor_code = #{checkDoctorCode}, check_doctor_name = #{checkDoctorName}
        where customer_reg_id = #{regId}
        <if test="statusLimit != null and statusLimit != ''">
            and check_status = #{statusLimit}
        </if>
        and (
        <foreach collection="itemGroupIds" item="itemGroupId" index="index" separator=" or ">
            (item_group_id = #{itemGroupId}
            <if test="checkPartCodes != null and checkPartCodes.size() > index">
                and (check_part_code = #{checkPartCodes[${index}]} or check_part_code is null)
            </if>)
        </foreach>
        )
    </update>

    <select id="statCustomerRegItemGroupStatus" resultType="org.jeecg.modules.station.bo.StatusStat">
        SELECT check_status as status, COUNT(*) as count
        FROM customer_reg_item_group
        WHERE abandon_flag=0 and add_minus_flag!=-1
        <if test="regId!=null and regId!=''"> AND customer_reg_id = #{regId}</if>
        <if test="departmentIds != null and departmentIds.size()>0">  and department_id in
          <foreach collection="departmentIds" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>
        GROUP BY check_status;
    </select>
    <select id="statCustomerRegItemGroupInterfaceStatus" resultType="org.jeecg.modules.station.bo.StatusStat">
        SELECT interface_sync_status as status, COUNT(*) as count
        FROM customer_reg_item_group
        WHERE abandon_flag=0 and add_minus_flag!=-1
        <if test="regId!=null and regId!=''"> AND customer_reg_id = #{regId}</if>
        <if test="departmentIds != null and departmentIds.size()>0">  and department_id in
            <foreach collection="departmentIds" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>
        GROUP BY interface_sync_status;
    </select>
    <select id="statCustomerRegItemGroupAbandoned" resultType="org.jeecg.modules.station.bo.StatusStat">
        select '放弃' as status,count(*)  as count FROM customer_reg_item_group
        WHERE abandon_flag=1
        <if test="regId!=null and regId!=''"> AND customer_reg_id = #{regId} </if>
        <if test="departmentIds != null and departmentIds.size()>0">  and department_id in
            <foreach collection="departmentIds" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>
    </select>

    <select id="listByRegAndDepart" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        SELECT * FROM customer_reg_item_group
        WHERE customer_reg_id = #{customerRegId}
        <if test="departmentIds != null and departmentIds.size()>0">  and department_id in
            <foreach collection="departmentIds" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>
    </select>

    <select id="listWithItemGroupByReg" resultMap="customerRegItemGroupMap">
        SELECT g.*,ig.id as ig_id,ig.department_id as ig_department_id,ig.his_code as ig_his_code,ig.min_discount_rate ig_min_discount_rate,
               ig.intro as ig_intro,ig.sort as ig_sort,ig.name as ig_name,ig.breakfast_flag as ig_breakfast_flag,ig.capacity_per_day as ig_capacity_per_day,
               ig.class_code as ig_class_code,ig.fasts_flag as ig_fasts_flag,ig.his_name as ig_his_name,ig.inquire_voluntarily_flag as ig_inquire_voluntarily_flag,
               ig.draw_blood_flag as ig_draw_blood_flag,ig.gynecological_flag as ig_gynecological_flag,ig.intro as ig_intro,ig.notice as ig_notice,ig.remark as ig_remark,ig.report_pic_attachable as ig_report_pic_attachable,ig.combine_summary_format as ig_combine_summary_format,ig.group_apply_flag as ig_group_apply_flag,ig.price as ig_price
        FROM customer_reg_item_group g join item_group ig on g.item_group_id = ig.id
        WHERE g.customer_reg_id = #{customerRegId}  and g.pay_status != '退款成功' and g.add_minus_flag!=-1
        <if test="!containFeeOnly"> and ig.charge_item_only_flag != 1</if>
        <if test="departmentIds != null and departmentIds.size()>0">  and g.department_id in
            <foreach collection="departmentIds" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>
        order by ig.sort
    </select>



    <select id="getCheckConclusion" resultType="java.lang.String">
        SELECT check_conclusion FROM customer_reg_item_group WHERE id = #{id}
    </select>

    <select id="getByRegAndGroup" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        SELECT * FROM customer_reg_item_group WHERE customer_reg_id = #{regId} AND item_group_id = #{groupId} limit 1
    </select>

    <select id="statPrice4Reg" resultType="org.jeecg.modules.reg.bo.PriceStat">
        select sum(price) as total_price,sum(price_after_dis) as total_price_after_dis from customer_reg_item_group where customer_reg_id= #{regId}
    </select>

    <select id="listByCompanyRegId" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        select g.*,c.team_name,c.team_id,c.company_id,c.company_name ,c.company_reg_id from customer_reg_item_group g join customer_reg c on g.customer_reg_id=c.id where  payer_type='单位支付' and c.company_reg_id=#{companyRegId}
    </select>
    <select id="listByIds" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        select g.* ,ig.charge_item_only_flag as chargeItemOnlyFlag from customer_reg_item_group g join item_group ig on g.item_group_id=ig.id where g.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listByFeeRecordId" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        select * from customer_reg_item_group where fee_record_id = #{feeRecordId}
    </select>
    <select id="getTotalAmount" resultType="java.math.BigDecimal">
        select sum(price_after_dis) from customer_reg_item_group where id in <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="checkStatGroupByDepart" resultType="org.jeecg.modules.station.bo.DepartStat">
        SELECT g.department_id, g.department_name,CASE WHEN COUNT(d.id) > 0 THEN '1' ELSE '0' END AS summary_status
        FROM customer_reg_item_group g join item_group ig on g.item_group_id = ig.id LEFT JOIN customer_reg_depart_summary d  ON g.customer_reg_id = d.customer_reg_id AND g.department_id = d.department_id
        WHERE g.customer_reg_id = #{customerRegId} and g.add_minus_flag!=-1 and ig.charge_item_only_flag != 1
        GROUP BY g.department_id, g.department_name;
    </select>
    <select id="listByBillId" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        select * from customer_reg_item_group where bill_id = #{billId}
    </select>
    <select id="listByReg" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        select * from customer_reg_item_group where customer_reg_id = #{customerRegId} and add_minus_flag != -1 <if test="payStatus!=null and payStatus!=''"> and pay_status=#{payStatus}</if>
    </select>
    <select id="listLiteByRegId" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        SELECT cg.id,cg.customer_reg_id,cg.item_group_id,cg.item_group_name,cg.department_id,cg.check_status,g.charge_item_only_flag,cg.check_part_code,cg.check_part_name,cg.check_part_id FROM customer_reg_item_group cg join item_group g on cg.item_group_id = g.id WHERE cg.customer_reg_id = #{customerRegId}  and cg.pay_status != '退款成功' and cg.add_minus_flag!=-1 and cg.abandon_flag!=1
    </select>
    <select id="getInterfaceResultByRegGroupId"
            resultType="org.jeecg.modules.interfacecheck.entity.InterfaceResult">
        SELECT
            i.interface_text,
            i.create_time
        FROM interface_result i
        where i.reg_group_id=#{regGroupId}
    </select>
    <select id="regGroupsGroupByDepart" resultType="org.jeecg.modules.station.bo.DepartStat">
        SELECT g.department_id, g.department_name
        FROM customer_reg_item_group g join item_group ig on g.item_group_id = ig.id
        WHERE g.customer_reg_id = #{customerRegId} and g.add_minus_flag!=-1 and ig.charge_item_only_flag != 1
        GROUP BY g.department_id, g.department_name;
    </select>
    <select id="getGroupsByRegIdAndDepartId" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        SELECT
           g.*
        FROM customer_reg_item_group g
                 join item_group ig on g.item_group_id = ig.id
        where  g.add_minus_flag!=-1 and ig.push_check_flag = 1
        <if test="checkStatus != null and checkStatus !=''">
              and g.check_status =#{checkStatus}
        </if>
               /*and ((g.payer_type='个人支付' and  g.pay_status='已支付') or g.payer_type='单位支付' or g.pay_status='退款成功' )*/
          and g.customer_reg_id=#{regId}
          and g.department_id=#{departmentId}
    </select>
    <select id="listIdByBillId" resultType="java.lang.String">
        select id from customer_reg_item_group where bill_id=#{billId}
    </select>
    <select id="getGroupByPsySuitIdAndRegId" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        select g.* from customer_reg_item_group g join quest_suit s on g.his_code=s.code where g.customer_reg_id=#{regId} and s.id=#{psySuitId}
    </select>
    <select id="listWithItemGroupByRegIds" resultMap="customerRegItemGroupMap">
        SELECT g.*,ig.id as ig_id,ig.department_id as ig_department_id,ig.his_code as ig_his_code,ig.min_discount_rate ig_min_discount_rate,
        ig.intro as ig_intro,ig.sort as ig_sort,ig.name as ig_name,ig.breakfast_flag as ig_breakfast_flag,ig.capacity_per_day as ig_capacity_per_day,
        ig.class_code as ig_class_code,ig.fasts_flag as ig_fasts_flag,ig.his_name as ig_his_name,ig.inquire_voluntarily_flag as ig_inquire_voluntarily_flag,
        ig.draw_blood_flag as ig_draw_blood_flag,ig.gynecological_flag as ig_gynecological_flag,ig.intro as ig_intro,ig.notice as ig_notice,ig.remark as ig_remark,ig.report_pic_attachable as ig_report_pic_attachable,ig.combine_summary_format as ig_combine_summary_format,ig.group_apply_flag as ig_group_apply_flag,ig.price as ig_price
        FROM customer_reg_item_group g join item_group ig on g.item_group_id = ig.id
        WHERE  g.pay_status != '退款成功' and g.add_minus_flag!=-1
        <if test="!containFeeOnly"> and ig.charge_item_only_flag != 1</if>
        <if test="regIds != null and regIds.size()>0">  and g.customer_reg_id  in
            <foreach collection="regIds" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>
        order by ig.sort
    </select>
    <select id="getRelationGroupByRegIdAndGroupId"
            resultType="org.jeecg.modules.basicinfo.entity.ItemGroupRelation">
        select distinct gr.* from customer_reg_item_group g join item_group_relation gr on g.item_group_id = gr.relation_group_id
        where g.customer_reg_id=#{regId} and gr.group_id=#{groupId}
    </select>


    <resultMap id="customerRegItemGroupMap" type="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        <id column="id" property="id" />
        <result column="customer_reg_id" property="customerRegId" />
        <result column="exam_no" property="examNo" />
        <result column="item_group_id" property="itemGroupId" />
        <result column="item_group_name" property="itemGroupName" />
        <result column="department_id" property="departmentId" />
        <result column="department_name" property="departmentName" />
        <result column="department_code" property="departmentCode" />
        <result column="type" property="type" />
        <result column="item_suit_id" property="itemSuitId" />
        <result column="item_suit_name" property="itemSuitName" />
        <result column="add_minus_flag" property="addMinusFlag" />
        <result column="price" property="price" />
        <result column="dis_rate" property="disRate" />
        <result column="price_after_dis" property="priceAfterDis" />
        <result column="price_dis_diff_amount" property="priceDisDiffAmount" />
        <result column="min_discount_rate" property="minDiscountRate" />
        <result column="payer_type" property="payerType" />
        <result column="pay_status" property="payStatus" />
        <result column="check_time" property="checkTime" />
        <result column="check_status" property="checkStatus" />
        <result column="reg_by" property="regBy" />
        <result column="depart_report_status" property="departReportStatus" />
        <result column="receipt_id" property="receiptId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="his_code" property="hisCode" />
        <result column="his_name" property="hisName" />
        <result column="plat_name" property="platName" />
        <result column="plat_code" property="platCode" />
        <result column="fee_record_id" property="feeRecordId" />
        <result column="refund_fee_record_id" property="refundFeeRecordId" />
        <result column="class_code" property="classCode" />
        <result column="abandon_flag" property="abandonFlag" />
        <result column="barcode_id" property="barcodeId" />
        <result column="check_conclusion" property="checkConclusion" />
        <result column="report_pdf" property="reportPdf" />
        <result column="report_pdf_interface" property="reportPdfInterface" />
        <result column="report_pics" property="reportPics" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="report_pics_dicom" property="reportPicsDicom" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="pic" property="pic" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="report_doctor_code" property="reportDoctorCode" />
        <result column="report_doctor_name" property="reportDoctorName" />
        <result column="audit_doctor_code" property="auditDoctorCode" />
        <result column="audit_doctor_name" property="auditDoctorName" />
        <result column="check_doctor_code" property="checkDoctorCode" />
        <result column="check_doctor_name" property="checkDoctorName" />
        <result column="report_time" property="reportTime" />
        <result column="check_bill_no" property="checkBillNo" />
        <result column="abnormal_flag" property="abnormalFlag" />
        <result column="check_part_id" property="checkPartId" />
        <result column="check_part_name" property="checkPartName" />
        <result column="check_part_code" property="checkPartCode" />
        <result column="parent_group_id" property="parentGroupId" />


        <association property="itemGroup" javaType="org.jeecg.modules.basicinfo.entity.ItemGroup" columnPrefix="ig_">
            <id column="id" property="id" />
            <result column="sort" property="sort" />
            <result column="name" property="name" />
            <result column="help_char" property="helpChar" />
            <result column="his_name" property="hisName" />
            <result column="his_code" property="hisCode" />
            <result column="plat_name" property="platName" />
            <result column="plat_code" property="platCode" />
            <result column="sex_limit" property="sexLimit" />
            <result column="min_age" property="minAge" />
            <result column="max_age" property="maxAge" />
            <result column="min_discount_rate" property="minDiscountRate" />
            <result column="price" property="price" />
            <result column="cost_price" property="costPrice" />
            <result column="department_id" property="departmentId" />
            <result column="department_name" property="departmentName" />
            <result column="department_code" property="departmentCode" />
            <result column="fee_type" property="feeType" />
            <result column="capacity_per_day" property="capacityPerDay" />
            <result column="combine_summary_format" property="combineSummaryFormat" />
            <result column="normal_summary_format" property="normalSummaryFormat" />
            <result column="clinical_type" property="clinicalType" />
            <result column="group_apply_flag" property="groupApplyFlag" />
            <result column="charge_item_only_flag" property="chargeItemOnlyFlag" />
            <result column="sys_item_flag" property="sysItemFlag" />
            <result column="item_sumable_flag" property="itemSumableFlag" />
            <result column="item_normalval_sumable_flag" property="itemNormalvalSumableFlag" />
            <result column="privacy_flag" property="privacyFlag" />
            <result column="draw_blood_flag" property="drawBloodFlag" />
            <result column="fasts_flag" property="fastsFlag" />
            <result column="gynecological_flag" property="gynecologicalFlag" />
            <result column="pregnancy_flag" property="pregnancyFlag" />
            <result column="breakfast_flag" property="breakfastFlag" />
            <result column="vip_flag" property="vipFlag" />
            <result column="outward_delivery_flag" property="outwardDeliveryFlag" />
            <result column="enable_flag" property="enableFlag" />
            <result column="inquire_voluntarily_flag" property="inquireVoluntarilyFlag" />
            <result column="stat_count" property="statCount" />
            <result column="notice" property="notice" />
            <result column="intro" property="intro" />
            <result column="remark" property="remark" />
            <result column="update_time" property="updateTime" />
            <result column="update_by" property="updateBy" />
            <result column="create_by" property="createBy" />
            <result column="create_time" property="createTime" />
            <result column="del_flag" property="delFlag" />
            <result column="class_code" property="classCode" />
            <result column="price_after_dis_of_suit" property="priceAfterDisOfSuit" />
            <result column="price_dis_diff_amount_of_suit" property="priceDisDiffAmountOfSuit" />
            <result column="min_discount_rate_of_suit" property="minDiscountRateOfSuit" />
            <result column="dis_rate_of_suit" property="disRateOfSuit" />
            <result column="unit" property="unit" />
            <result column="report_pic_attachable" property="reportPicAttachable" />
            <result column="group_apply_flag" property="groupApplyFlag" />
        </association>
    </resultMap>

</mapper>