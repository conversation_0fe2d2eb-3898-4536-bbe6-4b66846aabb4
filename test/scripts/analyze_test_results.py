#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebDriverPool压力测试结果分析工具
自动分析JMeter测试结果，生成性能报告
"""

import os
import sys
import csv
import json
import argparse
import statistics
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd

class TestResultAnalyzer:
    def __init__(self, results_dir, reports_dir):
        self.results_dir = Path(results_dir)
        self.reports_dir = Path(reports_dir)
        self.reports_dir.mkdir(exist_ok=True)
        
    def analyze_jtl_file(self, jtl_file):
        """分析JTL结果文件"""
        print(f"分析文件: {jtl_file}")
        
        data = []
        with open(jtl_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    data.append({
                        'timestamp': int(row['timeStamp']),
                        'elapsed': int(row['elapsed']),
                        'label': row['label'],
                        'responseCode': row['responseCode'],
                        'success': row['success'].lower() == 'true',
                        'bytes': int(row.get('bytes', 0)),
                        'sentBytes': int(row.get('sentBytes', 0)),
                        'latency': int(row.get('Latency', 0)),
                        'connect': int(row.get('Connect', 0))
                    })
                except (ValueError, KeyError) as e:
                    print(f"跳过无效行: {e}")
                    continue
        
        if not data:
            print(f"警告: {jtl_file} 中没有有效数据")
            return None
            
        return self.calculate_metrics(data)
    
    def calculate_metrics(self, data):
        """计算性能指标"""
        total_requests = len(data)
        successful_requests = sum(1 for d in data if d['success'])
        failed_requests = total_requests - successful_requests
        
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        
        # 响应时间统计
        response_times = [d['elapsed'] for d in data]
        avg_response_time = statistics.mean(response_times) if response_times else 0
        min_response_time = min(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        
        # 计算百分位数
        response_times_sorted = sorted(response_times)
        p50 = self.percentile(response_times_sorted, 50)
        p90 = self.percentile(response_times_sorted, 90)
        p95 = self.percentile(response_times_sorted, 95)
        p99 = self.percentile(response_times_sorted, 99)
        
        # 时间范围和吞吐量
        if data:
            start_time = min(d['timestamp'] for d in data)
            end_time = max(d['timestamp'] for d in data)
            duration_seconds = (end_time - start_time) / 1000.0
            throughput = total_requests / duration_seconds if duration_seconds > 0 else 0
        else:
            duration_seconds = 0
            throughput = 0
        
        # 错误分析
        error_codes = {}
        for d in data:
            if not d['success']:
                code = d['responseCode']
                error_codes[code] = error_codes.get(code, 0) + 1
        
        return {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'min_response_time': min_response_time,
            'max_response_time': max_response_time,
            'p50_response_time': p50,
            'p90_response_time': p90,
            'p95_response_time': p95,
            'p99_response_time': p99,
            'duration_seconds': duration_seconds,
            'throughput_tps': throughput,
            'error_codes': error_codes,
            'raw_data': data
        }
    
    def percentile(self, data, percent):
        """计算百分位数"""
        if not data:
            return 0
        k = (len(data) - 1) * percent / 100.0
        f = int(k)
        c = k - f
        if f == len(data) - 1:
            return data[f]
        return data[f] * (1 - c) + data[f + 1] * c
    
    def analyze_monitoring_logs(self, log_file):
        """分析系统监控日志"""
        if not os.path.exists(log_file):
            return None
            
        chrome_processes = []
        memory_usage = []
        cpu_usage = []
        
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                
                # 解析Chrome进程数量
                if 'Chrome进程总数:' in line:
                    try:
                        count = int(line.split(':')[1].strip())
                        chrome_processes.append(count)
                    except (ValueError, IndexError):
                        pass
                
                # 解析内存使用（简化版本）
                if 'Mem:' in line and 'used' in line:
                    # 这里需要根据实际日志格式调整
                    pass
        
        return {
            'chrome_processes': chrome_processes,
            'max_chrome_processes': max(chrome_processes) if chrome_processes else 0,
            'avg_chrome_processes': statistics.mean(chrome_processes) if chrome_processes else 0,
            'memory_usage': memory_usage,
            'cpu_usage': cpu_usage
        }
    
    def generate_charts(self, metrics, scenario_name):
        """生成性能图表"""
        charts_dir = self.reports_dir / 'charts'
        charts_dir.mkdir(exist_ok=True)
        
        # 响应时间分布图
        if metrics['raw_data']:
            response_times = [d['elapsed'] for d in metrics['raw_data']]
            
            plt.figure(figsize=(12, 8))
            
            # 响应时间分布直方图
            plt.subplot(2, 2, 1)
            plt.hist(response_times, bins=50, alpha=0.7, color='blue')
            plt.title(f'{scenario_name} - 响应时间分布')
            plt.xlabel('响应时间 (ms)')
            plt.ylabel('请求数量')
            
            # 响应时间趋势图
            plt.subplot(2, 2, 2)
            timestamps = [d['timestamp'] for d in metrics['raw_data']]
            plt.plot(timestamps, response_times, alpha=0.6)
            plt.title(f'{scenario_name} - 响应时间趋势')
            plt.xlabel('时间戳')
            plt.ylabel('响应时间 (ms)')
            
            # 成功率饼图
            plt.subplot(2, 2, 3)
            labels = ['成功', '失败']
            sizes = [metrics['successful_requests'], metrics['failed_requests']]
            colors = ['green', 'red']
            plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%')
            plt.title(f'{scenario_name} - 成功率')
            
            # 吞吐量图（如果有时间序列数据）
            plt.subplot(2, 2, 4)
            # 简化版本：显示平均吞吐量
            plt.bar(['吞吐量'], [metrics['throughput_tps']], color='orange')
            plt.title(f'{scenario_name} - 吞吐量')
            plt.ylabel('TPS')
            
            plt.tight_layout()
            chart_file = charts_dir / f'{scenario_name}_performance.png'
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"图表已生成: {chart_file}")
    
    def generate_report(self, all_metrics, monitoring_data=None):
        """生成综合报告"""
        report_file = self.reports_dir / f'performance_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html'
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebDriverPool压力测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .metrics-table {{ border-collapse: collapse; width: 100%; }}
        .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .metrics-table th {{ background-color: #f2f2f2; }}
        .success {{ color: green; }}
        .warning {{ color: orange; }}
        .error {{ color: red; }}
        .chart {{ text-align: center; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>WebDriverPool压力测试报告</h1>
        <p>生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>
    
    <div class="section">
        <h2>测试摘要</h2>
        <table class="metrics-table">
            <tr><th>测试场景</th><th>总请求数</th><th>成功率</th><th>平均响应时间</th><th>95%响应时间</th><th>吞吐量(TPS)</th></tr>
"""
        
        for scenario, metrics in all_metrics.items():
            success_class = "success" if metrics['success_rate'] >= 95 else "warning" if metrics['success_rate'] >= 90 else "error"
            response_class = "success" if metrics['avg_response_time'] <= 20000 else "warning" if metrics['avg_response_time'] <= 30000 else "error"
            
            html_content += f"""
            <tr>
                <td>{scenario}</td>
                <td>{metrics['total_requests']}</td>
                <td class="{success_class}">{metrics['success_rate']:.2f}%</td>
                <td class="{response_class}">{metrics['avg_response_time']:.0f}ms</td>
                <td>{metrics['p95_response_time']:.0f}ms</td>
                <td>{metrics['throughput_tps']:.2f}</td>
            </tr>
"""
        
        html_content += """
        </table>
    </div>
"""
        
        # 详细指标
        for scenario, metrics in all_metrics.items():
            html_content += f"""
    <div class="section">
        <h2>{scenario} - 详细指标</h2>
        <table class="metrics-table">
            <tr><td>总请求数</td><td>{metrics['total_requests']}</td></tr>
            <tr><td>成功请求数</td><td>{metrics['successful_requests']}</td></tr>
            <tr><td>失败请求数</td><td>{metrics['failed_requests']}</td></tr>
            <tr><td>成功率</td><td>{metrics['success_rate']:.2f}%</td></tr>
            <tr><td>平均响应时间</td><td>{metrics['avg_response_time']:.2f}ms</td></tr>
            <tr><td>最小响应时间</td><td>{metrics['min_response_time']}ms</td></tr>
            <tr><td>最大响应时间</td><td>{metrics['max_response_time']}ms</td></tr>
            <tr><td>50%响应时间</td><td>{metrics['p50_response_time']:.2f}ms</td></tr>
            <tr><td>90%响应时间</td><td>{metrics['p90_response_time']:.2f}ms</td></tr>
            <tr><td>95%响应时间</td><td>{metrics['p95_response_time']:.2f}ms</td></tr>
            <tr><td>99%响应时间</td><td>{metrics['p99_response_time']:.2f}ms</td></tr>
            <tr><td>测试持续时间</td><td>{metrics['duration_seconds']:.2f}秒</td></tr>
            <tr><td>吞吐量</td><td>{metrics['throughput_tps']:.2f} TPS</td></tr>
        </table>
        
        <div class="chart">
            <img src="charts/{scenario}_performance.png" alt="{scenario}性能图表" style="max-width: 100%;">
        </div>
    </div>
"""
        
        # 监控数据
        if monitoring_data:
            html_content += f"""
    <div class="section">
        <h2>系统监控数据</h2>
        <table class="metrics-table">
            <tr><td>最大Chrome进程数</td><td>{monitoring_data.get('max_chrome_processes', 'N/A')}</td></tr>
            <tr><td>平均Chrome进程数</td><td>{monitoring_data.get('avg_chrome_processes', 'N/A'):.1f}</td></tr>
        </table>
    </div>
"""
        
        html_content += """
</body>
</html>
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"报告已生成: {report_file}")
        return report_file
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始分析测试结果...")
        
        # 查找所有JTL文件
        jtl_files = list(self.results_dir.glob('*.jtl'))
        if not jtl_files:
            print("未找到JTL结果文件")
            return
        
        all_metrics = {}
        
        for jtl_file in jtl_files:
            scenario_name = jtl_file.stem
            metrics = self.analyze_jtl_file(jtl_file)
            
            if metrics:
                all_metrics[scenario_name] = metrics
                self.generate_charts(metrics, scenario_name)
        
        # 分析监控日志
        monitoring_data = None
        log_files = list(Path('logs').glob('system_monitor_*.log'))
        if log_files:
            latest_log = max(log_files, key=os.path.getctime)
            monitoring_data = self.analyze_monitoring_logs(latest_log)
        
        # 生成报告
        if all_metrics:
            report_file = self.generate_report(all_metrics, monitoring_data)
            print(f"\n分析完成！")
            print(f"报告文件: {report_file}")
            print(f"图表目录: {self.reports_dir}/charts/")
        else:
            print("没有有效的测试数据可分析")

def main():
    parser = argparse.ArgumentParser(description='WebDriverPool压力测试结果分析工具')
    parser.add_argument('--results-dir', default='results', help='测试结果目录')
    parser.add_argument('--reports-dir', default='reports', help='报告输出目录')
    
    args = parser.parse_args()
    
    # 检查依赖
    try:
        import matplotlib.pyplot as plt
        import pandas as pd
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请安装: pip install matplotlib pandas")
        sys.exit(1)
    
    analyzer = TestResultAnalyzer(args.results_dir, args.reports_dir)
    analyzer.run_analysis()

if __name__ == '__main__':
    main()
