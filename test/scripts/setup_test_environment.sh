#!/bin/bash

# WebDriverPool压力测试环境设置脚本
# 自动化配置测试环境，包括依赖检查、工具安装等

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
TEST_DIR="$PROJECT_ROOT/test"

# JMeter版本配置
JMETER_VERSION="5.4.3"
JMETER_URL="https://archive.apache.org/dist/jmeter/binaries/apache-jmeter-${JMETER_VERSION}.tgz"
JMETER_HOME="/opt/apache-jmeter-${JMETER_VERSION}"

# Chrome和ChromeDriver配置
CHROME_DRIVER_VERSION="latest"

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_warn() {
    echo -e "\033[33m[WARN]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 检查操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        if [ -f /etc/debian_version ]; then
            DISTRO="debian"
        elif [ -f /etc/redhat-release ]; then
            DISTRO="redhat"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        print_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    print_info "检测到操作系统: $OS ($DISTRO)"
}

# 检查并安装基础依赖
install_dependencies() {
    print_info "检查并安装基础依赖..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        print_error "Java未安装，请先安装Java 8或更高版本"
        exit 1
    else
        local java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
        print_info "Java版本: $java_version"
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        print_info "安装curl..."
        if [ "$DISTRO" = "debian" ]; then
            sudo apt-get update && sudo apt-get install -y curl
        elif [ "$DISTRO" = "redhat" ]; then
            sudo yum install -y curl
        elif [ "$OS" = "macos" ]; then
            brew install curl
        fi
    fi
    
    # 检查wget
    if ! command -v wget &> /dev/null; then
        print_info "安装wget..."
        if [ "$DISTRO" = "debian" ]; then
            sudo apt-get install -y wget
        elif [ "$DISTRO" = "redhat" ]; then
            sudo yum install -y wget
        elif [ "$OS" = "macos" ]; then
            brew install wget
        fi
    fi
    
    # 检查unzip
    if ! command -v unzip &> /dev/null; then
        print_info "安装unzip..."
        if [ "$DISTRO" = "debian" ]; then
            sudo apt-get install -y unzip
        elif [ "$DISTRO" = "redhat" ]; then
            sudo yum install -y unzip
        elif [ "$OS" = "macos" ]; then
            brew install unzip
        fi
    fi
}

# 安装Chrome浏览器
install_chrome() {
    print_info "检查Chrome浏览器..."
    
    if command -v google-chrome &> /dev/null || command -v chromium-browser &> /dev/null; then
        print_info "Chrome浏览器已安装"
        return 0
    fi
    
    print_info "安装Chrome浏览器..."
    
    if [ "$DISTRO" = "debian" ]; then
        # Ubuntu/Debian
        wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
        sudo apt-get update
        sudo apt-get install -y google-chrome-stable
    elif [ "$DISTRO" = "redhat" ]; then
        # CentOS/RHEL
        sudo yum install -y https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
    elif [ "$OS" = "macos" ]; then
        # macOS
        brew install --cask google-chrome
    fi
    
    if command -v google-chrome &> /dev/null; then
        local chrome_version=$(google-chrome --version)
        print_info "Chrome安装成功: $chrome_version"
    else
        print_error "Chrome安装失败"
        exit 1
    fi
}

# 安装ChromeDriver
install_chromedriver() {
    print_info "检查ChromeDriver..."
    
    if command -v chromedriver &> /dev/null; then
        local driver_version=$(chromedriver --version)
        print_info "ChromeDriver已安装: $driver_version"
        return 0
    fi
    
    print_info "安装ChromeDriver..."
    
    # 获取Chrome版本
    local chrome_version=""
    if command -v google-chrome &> /dev/null; then
        chrome_version=$(google-chrome --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    elif command -v chromium-browser &> /dev/null; then
        chrome_version=$(chromium-browser --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    fi
    
    if [ -z "$chrome_version" ]; then
        print_error "无法获取Chrome版本"
        exit 1
    fi
    
    print_info "Chrome版本: $chrome_version"
    
    # 获取对应的ChromeDriver版本
    local driver_version=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${chrome_version%.*}")
    
    if [ -z "$driver_version" ]; then
        print_warn "无法获取对应的ChromeDriver版本，使用最新版本"
        driver_version=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE")
    fi
    
    print_info "下载ChromeDriver版本: $driver_version"
    
    # 下载ChromeDriver
    local download_url=""
    if [ "$OS" = "linux" ]; then
        download_url="https://chromedriver.storage.googleapis.com/${driver_version}/chromedriver_linux64.zip"
    elif [ "$OS" = "macos" ]; then
        download_url="https://chromedriver.storage.googleapis.com/${driver_version}/chromedriver_mac64.zip"
    fi
    
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    wget -q "$download_url" -O chromedriver.zip
    unzip -q chromedriver.zip
    
    # 安装ChromeDriver
    sudo mv chromedriver /usr/local/bin/
    sudo chmod +x /usr/local/bin/chromedriver
    
    cd - > /dev/null
    rm -rf "$temp_dir"
    
    if command -v chromedriver &> /dev/null; then
        local installed_version=$(chromedriver --version)
        print_info "ChromeDriver安装成功: $installed_version"
    else
        print_error "ChromeDriver安装失败"
        exit 1
    fi
}

# 安装JMeter
install_jmeter() {
    print_info "检查JMeter..."
    
    if [ -d "$JMETER_HOME" ]; then
        print_info "JMeter已安装: $JMETER_HOME"
        return 0
    fi
    
    print_info "下载并安装JMeter $JMETER_VERSION..."
    
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # 下载JMeter
    wget -q "$JMETER_URL" -O apache-jmeter.tgz
    
    if [ $? -ne 0 ]; then
        print_error "JMeter下载失败"
        exit 1
    fi
    
    # 解压并安装
    tar -xzf apache-jmeter.tgz
    sudo mv "apache-jmeter-${JMETER_VERSION}" /opt/
    
    # 创建符号链接
    sudo ln -sf "/opt/apache-jmeter-${JMETER_VERSION}" /opt/apache-jmeter
    
    cd - > /dev/null
    rm -rf "$temp_dir"
    
    if [ -d "$JMETER_HOME" ]; then
        print_info "JMeter安装成功: $JMETER_HOME"
        
        # 添加到PATH
        if ! grep -q "JMETER_HOME" ~/.bashrc; then
            echo "export JMETER_HOME=$JMETER_HOME" >> ~/.bashrc
            echo "export PATH=\$PATH:\$JMETER_HOME/bin" >> ~/.bashrc
            print_info "JMeter已添加到PATH，请重新加载shell或执行: source ~/.bashrc"
        fi
    else
        print_error "JMeter安装失败"
        exit 1
    fi
}

# 创建测试目录结构
create_test_directories() {
    print_info "创建测试目录结构..."
    
    local directories=(
        "$TEST_DIR/results"
        "$TEST_DIR/logs"
        "$TEST_DIR/reports"
        "$TEST_DIR/data"
        "$TEST_DIR/scripts"
        "$TEST_DIR/config"
        "$TEST_DIR/jmeter"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
}

# 设置权限
set_permissions() {
    print_info "设置脚本权限..."
    
    find "$TEST_DIR/scripts" -name "*.sh" -exec chmod +x {} \;
    
    print_info "权限设置完成"
}

# 验证安装
verify_installation() {
    print_info "验证安装..."
    
    local errors=0
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        print_error "Java未找到"
        errors=$((errors + 1))
    fi
    
    # 检查Chrome
    if ! command -v google-chrome &> /dev/null && ! command -v chromium-browser &> /dev/null; then
        print_error "Chrome浏览器未找到"
        errors=$((errors + 1))
    fi
    
    # 检查ChromeDriver
    if ! command -v chromedriver &> /dev/null; then
        print_error "ChromeDriver未找到"
        errors=$((errors + 1))
    fi
    
    # 检查JMeter
    if [ ! -d "$JMETER_HOME" ]; then
        print_error "JMeter未找到: $JMETER_HOME"
        errors=$((errors + 1))
    fi
    
    # 检查测试文件
    local required_files=(
        "$TEST_DIR/jmeter/webdriver-pool-test.jmx"
        "$TEST_DIR/data/customer_reg_ids.csv"
        "$TEST_DIR/scripts/monitor_system.sh"
        "$TEST_DIR/scripts/run_pressure_test.sh"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "测试文件未找到: $file"
            errors=$((errors + 1))
        fi
    done
    
    if [ $errors -eq 0 ]; then
        print_info "所有组件验证通过"
        return 0
    else
        print_error "发现 $errors 个问题，请检查安装"
        return 1
    fi
}

# 显示安装摘要
show_summary() {
    print_info "安装摘要:"
    echo "  项目根目录: $PROJECT_ROOT"
    echo "  测试目录: $TEST_DIR"
    echo "  JMeter安装路径: $JMETER_HOME"
    echo "  Chrome路径: $(which google-chrome || which chromium-browser || echo '未找到')"
    echo "  ChromeDriver路径: $(which chromedriver || echo '未找到')"
    echo ""
    print_info "使用方法:"
    echo "  1. 启动应用服务器"
    echo "  2. 执行压力测试: cd $TEST_DIR && ./scripts/run_pressure_test.sh"
    echo "  3. 查看测试结果: $TEST_DIR/reports/"
}

# 主函数
main() {
    print_info "WebDriverPool压力测试环境设置开始"
    
    # 检测操作系统
    detect_os
    
    # 安装基础依赖
    install_dependencies
    
    # 安装Chrome
    install_chrome
    
    # 安装ChromeDriver
    install_chromedriver
    
    # 安装JMeter
    install_jmeter
    
    # 创建测试目录
    create_test_directories
    
    # 设置权限
    set_permissions
    
    # 验证安装
    if verify_installation; then
        print_info "测试环境设置完成"
        show_summary
    else
        print_error "测试环境设置失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "WebDriverPool压力测试环境设置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --skip-chrome  跳过Chrome安装"
    echo "  --skip-jmeter  跳过JMeter安装"
    echo "  --jmeter-home PATH  指定JMeter安装路径"
    echo ""
    echo "示例:"
    echo "  $0                    # 完整安装"
    echo "  $0 --skip-chrome     # 跳过Chrome安装"
    echo "  $0 --jmeter-home /opt/jmeter  # 指定JMeter路径"
}

# 解析命令行参数
SKIP_CHROME=false
SKIP_JMETER=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --skip-chrome)
            SKIP_CHROME=true
            shift
            ;;
        --skip-jmeter)
            SKIP_JMETER=true
            shift
            ;;
        --jmeter-home)
            JMETER_HOME="$2"
            shift 2
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
