package org.jeecg.modules.summary.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.logging.LoggingPreferences;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Level;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 统一的WebDriver池管理器
 * 解决多服务各自创建WebDriverPool导致的进程泄漏问题
 * 
 * <AUTHOR>
 * @since 2024-08-07
 */
@Component
public class WebDriverPoolManager {
    private static final Logger log = LoggerFactory.getLogger(WebDriverPoolManager.class);
    
    // 池配置
    private static final int DEFAULT_POOL_SIZE = 2;
    private static final int MAX_USAGE_COUNT = 15;
    private static final int MAX_RETRY_COUNT = 3;
    private static final long BORROW_TIMEOUT_SECONDS = 30;
    private static final long HEALTH_CHECK_INTERVAL_MINUTES = 5;
    private static final long PROCESS_CLEANUP_INTERVAL_MINUTES = 10;
    
    // 池和计数器
    private final BlockingQueue<WebDriver> pool = new LinkedBlockingQueue<>();
    private final Map<WebDriver, Integer> usageCounter = new ConcurrentHashMap<>();
    private final AtomicInteger activeDriverCount = new AtomicInteger(0);
    private final AtomicLong totalCreatedCount = new AtomicLong(0);
    private final AtomicLong totalDestroyedCount = new AtomicLong(0);
    
    // 配置参数
    @Value("${selenium.chromeDriverPath}")
    private String chromeDriverPath;
    
    @Value("${selenium.chromePath}")
    private String chromePath;
    
    @Value("${webdriver.pool.size:2}")
    private int poolSize = DEFAULT_POOL_SIZE;
    
    // 线程池和调度器
    private ExecutorService executorService;
    private ScheduledExecutorService scheduledExecutor;
    private volatile boolean isShutdown = false;

    @PostConstruct
    public void init() {
        log.info("初始化WebDriverPoolManager，池大小: {}", poolSize);
        
        // 初始化线程池
        executorService = Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "WebDriverPool-Worker");
            t.setDaemon(true);
            return t;
        });
        
        scheduledExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "WebDriverPool-Scheduler");
            t.setDaemon(true);
            return t;
        });
        
        // 初始化WebDriver池
        initializePool();
        
        // 启动定期健康检查
        startHealthCheck();
        
        // 启动进程清理任务
        startProcessCleanup();
        
        log.info("WebDriverPoolManager初始化完成");
    }

    /**
     * 初始化WebDriver池
     */
    private void initializePool() {
        for (int i = 0; i < poolSize; i++) {
            try {
                WebDriver driver = createWebDriver();
                pool.offer(driver);
                usageCounter.put(driver, 0);
                activeDriverCount.incrementAndGet();
                totalCreatedCount.incrementAndGet();
                log.info("创建WebDriver实例 {}/{}", i + 1, poolSize);
            } catch (Exception e) {
                log.error("初始化WebDriver池时创建实例失败", e);
            }
        }
    }

    /**
     * 创建WebDriver实例
     */
    private WebDriver createWebDriver() {
        return createWebDriverWithRetry(3);
    }

    /**
     * 带重试机制的WebDriver创建
     */
    private WebDriver createWebDriverWithRetry(int maxRetries) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("尝试创建Chrome实例，第 {}/{} 次", attempt, maxRetries);

                if (attempt == 1) {
                    // 第一次尝试：使用标准配置
                    return createWebDriverWithStandardConfig();
                } else if (attempt == 2) {
                    // 第二次尝试：使用简化配置
                    return createWebDriverWithMinimalConfig();
                } else {
                    // 第三次尝试：使用最基础配置
                    return createWebDriverWithBasicConfig();
                }

            } catch (Exception e) {
                lastException = e;
                log.warn("第 {} 次创建Chrome实例失败: {}", attempt, e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(2000 * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        throw new RuntimeException("经过 " + maxRetries + " 次尝试仍无法创建Chrome实例", lastException);
    }

    /**
     * 标准配置创建WebDriver
     */
    private WebDriver createWebDriverWithStandardConfig() {
        System.setProperty("webdriver.chrome.driver", chromeDriverPath);

        ChromeOptions options = new ChromeOptions();

        // 检测操作系统
        String osName = System.getProperty("os.name").toLowerCase();
        boolean isWindows = osName.contains("windows");

        // 基础配置
        options.addArguments("--headless=new");
        options.addArguments("--disable-gpu");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--remote-allow-origins=*");

        // Windows特殊处理
        if (isWindows) {
            options.addArguments("--no-first-run");
            options.addArguments("--disable-default-apps");
            options.addArguments("--window-size=1920,1080");

            // 创建临时用户数据目录
            String tempDir = System.getProperty("java.io.tmpdir");
            String userDataDir = tempDir + File.separator + "chrome_" + System.currentTimeMillis();
            options.addArguments("--user-data-dir=" + userDataDir);
        }

        // 内存优化
        options.addArguments("--js-flags=--max-old-space-size=512");
        options.addArguments("--memory-pressure-off");
        
        // 基础功能禁用（简化版本）
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-plugins");
        options.addArguments("--disable-images");
        options.addArguments("--disable-javascript");

        // 如果是Windows，进一步简化配置
        if (!isWindows) {
            // 非Windows环境可以使用更多优化参数
            options.addArguments("--disable-cache");
            options.addArguments("--disable-application-cache");
            options.addArguments("--disk-cache-size=0");
            options.addArguments("--disable-background-timer-throttling");
            options.addArguments("--disable-features=TranslateUI");
        }
        
        // 禁用自动化控制条
        options.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"});
        options.setExperimentalOption("useAutomationExtension", false);
        
        // 设置Chrome二进制文件路径
        if (StringUtils.isNotBlank(chromePath)) {
            options.setBinary(chromePath);
        }

        return createChromeDriver(options, "标准配置");
    }

    /**
     * 简化配置创建WebDriver
     */
    private WebDriver createWebDriverWithMinimalConfig() {
        System.setProperty("webdriver.chrome.driver", chromeDriverPath);

        ChromeOptions options = new ChromeOptions();

        // 最基础的无头模式配置
        options.addArguments("--headless=new");
        options.addArguments("--disable-gpu");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--remote-allow-origins=*");
        options.addArguments("--window-size=1280,720");

        // Windows环境特殊处理
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("windows")) {
            String tempDir = System.getProperty("java.io.tmpdir");
            String userDataDir = tempDir + File.separator + "chrome_minimal_" + System.currentTimeMillis();
            options.addArguments("--user-data-dir=" + userDataDir);
        }

        // 设置Chrome二进制文件路径
        if (StringUtils.isNotBlank(chromePath)) {
            options.setBinary(chromePath);
        }

        return createChromeDriver(options, "简化配置");
    }

    /**
     * 基础配置创建WebDriver
     */
    private WebDriver createWebDriverWithBasicConfig() {
        System.setProperty("webdriver.chrome.driver", chromeDriverPath);

        ChromeOptions options = new ChromeOptions();

        // 只保留最必要的参数
        options.addArguments("--headless=new");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");

        // 设置Chrome二进制文件路径
        if (StringUtils.isNotBlank(chromePath)) {
            options.setBinary(chromePath);
        }

        return createChromeDriver(options, "基础配置");
    }

    /**
     * 统一的ChromeDriver创建方法
     */
    private WebDriver createChromeDriver(ChromeOptions options, String configType) {
        log.info("使用{}创建Chrome实例", configType);

        ChromeDriver driver = new ChromeDriver(options);

        // 设置超时时间
        driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(60));
        driver.manage().timeouts().scriptTimeout(Duration.ofSeconds(5*60));
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));

        log.info("{}Chrome实例创建成功", configType);
        return driver;
    }



    /**
     * 使用备用配置创建WebDriver实例（Windows环境专用）
     */
    private WebDriver createWebDriverWithFallbackOptions() {
        log.info("使用备用配置创建Chrome实例");

        System.setProperty("webdriver.chrome.driver", chromeDriverPath);

        ChromeOptions options = new ChromeOptions();

        // 最小化配置，只保留必要参数
        options.addArguments("--headless=new");
        options.addArguments("--disable-gpu");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--remote-allow-origins=*");
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-plugins");
        options.addArguments("--disable-images");
        options.addArguments("--window-size=1280,720");

        // 设置用户数据目录到临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        String userDataDir = tempDir + "\\chrome_minimal_" + System.currentTimeMillis();
        options.addArguments("--user-data-dir=" + userDataDir);

        // 设置Chrome二进制文件路径
        if (StringUtils.isNotBlank(chromePath)) {
            options.setBinary(chromePath);
        }

        try {
            ChromeDriver driver = new ChromeDriver(options);

            // 设置超时时间
            driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(60));
            driver.manage().timeouts().scriptTimeout(Duration.ofSeconds(5*60));
            driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));

            log.info("备用配置Chrome实例创建成功");
            return driver;
        } catch (Exception e) {
            log.error("备用配置Chrome实例创建也失败: {}", e.getMessage());
            throw new RuntimeException("无法创建Chrome实例，请检查Chrome安装和配置", e);
        }
    }

    /**
     * 借用WebDriver实例
     */
    public WebDriver borrowWebDriver(String url) throws InterruptedException, TimeoutException {
        if (isShutdown) {
            throw new IllegalStateException("WebDriverPoolManager已关闭");
        }
        
        int retryCount = 0;
        while (retryCount < MAX_RETRY_COUNT) {
            try {
                WebDriver driver = pool.poll(BORROW_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                if (driver == null) {
                    throw new TimeoutException("获取WebDriver超时，池可能已满或所有实例都在使用中");
                }
                
                // 健康检查
                if (!isDriverHealthy(driver)) {
                    log.warn("检测到不健康的Chrome实例，重新创建");
                    safeDestroyDriver(driver);
                    driver = createReplacementDriver();
                }
                
                // 检查使用次数
                Integer count = usageCounter.getOrDefault(driver, 0);
                if (count >= MAX_USAGE_COUNT) {
                    log.info("Chrome实例达到最大使用次数: {}，重新创建", count);
                    safeDestroyDriver(driver);
                    driver = createReplacementDriver();
                    count = 0;
                }
                
                // 更新使用次数
                usageCounter.put(driver, count + 1);
                
                // 导航到指定URL
                navigateToUrl(driver, url);
                
                log.debug("成功借用WebDriver实例，当前使用次数: {}", count + 1);
                return driver;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw e;
            } catch (Exception e) {
                retryCount++;
                log.warn("借用WebDriver失败，重试次数: {}/{}", retryCount, MAX_RETRY_COUNT, e);
                if (retryCount >= MAX_RETRY_COUNT) {
                    throw new RuntimeException("借用WebDriver失败，已达到最大重试次数", e);
                }
                Thread.sleep(1000); // 等待1秒后重试
            }
        }
        
        throw new RuntimeException("借用WebDriver失败");
    }

    /**
     * 归还WebDriver实例
     */
    public void returnWebDriver(WebDriver driver) {
        if (driver == null || isShutdown) {
            return;
        }
        
        try {
            // 健康检查
            if (!isDriverHealthy(driver)) {
                log.warn("归还的WebDriver实例不健康，销毁并创建新实例");
                safeDestroyDriver(driver);
                createReplacementDriverAsync();
                return;
            }
            
            // 清理浏览器状态
            cleanupBrowserState(driver);
            
            // 归还到池中
            if (!pool.offer(driver)) {
                log.warn("池已满，销毁多余的WebDriver实例");
                safeDestroyDriver(driver);
            } else {
                log.debug("成功归还WebDriver实例到池中");
            }
            
        } catch (Exception e) {
            log.warn("归还WebDriver时发生异常，销毁实例", e);
            safeDestroyDriver(driver);
            createReplacementDriverAsync();
        }
    }

    /**
     * 安全销毁WebDriver实例
     */
    private void safeDestroyDriver(WebDriver driver) {
        if (driver == null) {
            return;
        }
        
        try {
            driver.quit();
            log.debug("成功销毁WebDriver实例");
        } catch (Exception e) {
            log.warn("销毁WebDriver实例时发生异常", e);
        } finally {
            usageCounter.remove(driver);
            activeDriverCount.decrementAndGet();
            totalDestroyedCount.incrementAndGet();
        }
    }

    /**
     * 创建替换的WebDriver实例
     */
    private WebDriver createReplacementDriver() {
        try {
            WebDriver newDriver = createWebDriver();
            usageCounter.put(newDriver, 0);
            activeDriverCount.incrementAndGet();
            totalCreatedCount.incrementAndGet();
            log.info("成功创建替换的WebDriver实例");
            return newDriver;
        } catch (Exception e) {
            log.error("创建替换WebDriver实例失败", e);
            throw new RuntimeException("创建替换WebDriver实例失败", e);
        }
    }

    /**
     * 异步创建替换的WebDriver实例
     */
    private void createReplacementDriverAsync() {
        if (isShutdown) {
            return;
        }

        executorService.submit(() -> {
            try {
                if (pool.size() < poolSize) {
                    WebDriver newDriver = createReplacementDriver();
                    pool.offer(newDriver);
                    log.info("异步创建并添加替换WebDriver实例到池中");
                }
            } catch (Exception e) {
                log.error("异步创建替换WebDriver实例失败", e);
            }
        });
    }

    /**
     * 检查WebDriver是否健康
     */
    private boolean isDriverHealthy(WebDriver driver) {
        if (driver == null) {
            return false;
        }

        try {
            // 1. 检查窗口句柄
            driver.getWindowHandle();

            // 2. 检查会话ID
            String sessionId = ((ChromeDriver) driver).getSessionId().toString();
            if (StringUtils.isEmpty(sessionId)) {
                return false;
            }

            // 3. 执行简单的JavaScript测试
            JavascriptExecutor js = (JavascriptExecutor) driver;
            Object result = js.executeScript("return 'health_check'");
            if (!"health_check".equals(result)) {
                return false;
            }

            // 4. 检查页面标题
            driver.getTitle();

            return true;

        } catch (org.openqa.selenium.NoSuchSessionException e) {
            log.debug("WebDriver会话已失效: {}", e.getMessage());
            return false;
        } catch (org.openqa.selenium.WebDriverException e) {
            log.debug("WebDriver连接异常: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.debug("WebDriver健康检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 清理浏览器状态
     */
    private void cleanupBrowserState(WebDriver driver) {
        try {
            // 清理cookies
            driver.manage().deleteAllCookies();

            // 清理本地存储和会话存储
            if (driver instanceof JavascriptExecutor) {
                JavascriptExecutor js = (JavascriptExecutor) driver;
                try {
                    js.executeScript("localStorage.clear();");
                    js.executeScript("sessionStorage.clear();");
                    js.executeScript("window.reportData = null; window.templateData = null;");

                    // 强制垃圾回收
                    js.executeScript("if (window.gc) { window.gc(); }");
                } catch (Exception ex) {
                    log.debug("清理浏览器状态时发生异常", ex);
                }
            }
        } catch (Exception e) {
            log.debug("清理浏览器状态失败", e);
        }
    }

    /**
     * 导航到指定URL
     */
    private void navigateToUrl(WebDriver driver, String url) {
        try {
            String currentUrl = driver.getCurrentUrl();
            if (!StringUtils.equals(currentUrl, url)) {
                log.debug("导航到URL: {}", url);
                driver.get(url);

                // 等待页面加载完成
                JavascriptExecutor js = (JavascriptExecutor) driver;
                long startTime = System.currentTimeMillis();
                while (System.currentTimeMillis() - startTime < 30000) {
                    try {
                        Boolean isPageLoaded = (Boolean) js.executeScript("return document.readyState === 'complete'");
                        if (Boolean.TRUE.equals(isPageLoaded)) {
                            break;
                        }
                        Thread.sleep(500);
                    } catch (Exception e) {
                        log.debug("检查页面加载状态时出错: {}", e.getMessage());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("导航到URL失败: {}", e.getMessage());
            throw new RuntimeException("页面导航失败", e);
        }
    }

    /**
     * 启动健康检查任务
     */
    private void startHealthCheck() {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            if (isShutdown) {
                return;
            }

            try {
                log.debug("开始WebDriver池健康检查");
                int unhealthyCount = 0;
                int totalChecked = 0;

                // 临时存储需要检查的driver
                java.util.List<WebDriver> driversToCheck = new java.util.ArrayList<>();

                // 从池中取出所有driver进行检查
                WebDriver driver;
                while ((driver = pool.poll()) != null) {
                    driversToCheck.add(driver);
                }

                // 检查每个driver的健康状态
                for (WebDriver d : driversToCheck) {
                    totalChecked++;
                    if (isDriverHealthy(d)) {
                        pool.offer(d); // 健康的放回池中
                    } else {
                        unhealthyCount++;
                        safeDestroyDriver(d);
                        // 创建新的替换实例
                        createReplacementDriverAsync();
                    }
                }

                if (unhealthyCount > 0) {
                    log.info("健康检查完成，检查了{}个实例，发现{}个不健康实例已重新创建", totalChecked, unhealthyCount);
                }

                // 记录池状态
                log.debug("WebDriver池状态 - 池大小: {}, 活跃实例: {}, 总创建: {}, 总销毁: {}",
                    pool.size(), activeDriverCount.get(), totalCreatedCount.get(), totalDestroyedCount.get());

            } catch (Exception e) {
                log.error("WebDriver池健康检查失败", e);
            }
        }, HEALTH_CHECK_INTERVAL_MINUTES, HEALTH_CHECK_INTERVAL_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 启动进程清理任务
     */
    private void startProcessCleanup() {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            if (isShutdown) {
                return;
            }

            try {
                cleanupZombieProcesses();
            } catch (Exception e) {
                log.error("清理僵尸进程失败", e);
            }
        }, PROCESS_CLEANUP_INTERVAL_MINUTES, PROCESS_CLEANUP_INTERVAL_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 清理僵尸Chrome进程
     */
    private void cleanupZombieProcesses() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            String command;

            if (os.contains("win")) {
                // Windows系统
                command = "tasklist /FI \"IMAGENAME eq chrome.exe\" /FO CSV";
            } else {
                // Linux/Unix系统
                command = "ps aux | grep chrome";
            }

            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            int chromeProcessCount = 0;
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.toLowerCase().contains("chrome")) {
                    chromeProcessCount++;
                }
            }

            reader.close();
            process.waitFor();

            log.debug("检测到{}个Chrome进程", chromeProcessCount);

            // 如果Chrome进程数量异常多（超过池大小的3倍），记录警告
            if (chromeProcessCount > poolSize * 3) {
                log.warn("检测到异常多的Chrome进程: {}，池大小: {}，活跃实例: {}",
                    chromeProcessCount, poolSize, activeDriverCount.get());

                // 可以在这里添加强制清理逻辑
                if (chromeProcessCount > poolSize * 5) {
                    log.error("Chrome进程数量严重超标，考虑重启WebDriver池");
                    // 这里可以触发池重启逻辑
                }
            }

        } catch (Exception e) {
            log.debug("检查Chrome进程时发生异常", e);
        }
    }

    /**
     * 强制清理所有Chrome进程（紧急情况使用）
     */
    public void forceCleanupAllChromeProcesses() {
        log.warn("执行强制清理所有Chrome进程");

        try {
            String os = System.getProperty("os.name").toLowerCase();
            String command;

            if (os.contains("win")) {
                command = "taskkill /F /IM chrome.exe";
            } else {
                command = "pkill -f chrome";
            }

            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("成功强制清理所有Chrome进程");
            } else {
                log.warn("强制清理Chrome进程返回非零退出码: {}", exitCode);
            }

            // 清空池和计数器
            pool.clear();
            usageCounter.clear();
            activeDriverCount.set(0);

            // 重新初始化池
            initializePool();

        } catch (Exception e) {
            log.error("强制清理Chrome进程失败", e);
        }
    }

    /**
     * 获取池状态信息
     */
    public String getPoolStatus() {
        return String.format("WebDriver池状态 - 池大小: %d, 活跃实例: %d, 总创建: %d, 总销毁: %d, 当前可用: %d",
            poolSize, activeDriverCount.get(), totalCreatedCount.get(), totalDestroyedCount.get(), pool.size());
    }

    /**
     * 关闭WebDriver池管理器
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始关闭WebDriverPoolManager");
        isShutdown = true;

        // 关闭调度器
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭执行器
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 清理所有WebDriver实例
        log.info("清理所有WebDriver实例，当前池大小: {}", pool.size());
        WebDriver driver;
        while ((driver = pool.poll()) != null) {
            safeDestroyDriver(driver);
        }

        // 清理计数器
        usageCounter.clear();
        activeDriverCount.set(0);

        log.info("WebDriverPoolManager关闭完成，总创建: {}, 总销毁: {}",
            totalCreatedCount.get(), totalDestroyedCount.get());
    }
}
