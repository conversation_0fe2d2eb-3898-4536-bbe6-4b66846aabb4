package org.jeecg.modules.summary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.UrlUtils;
import org.jeecg.modules.basicinfo.entity.Template;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.basicinfo.service.ITemplateService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.summary.bo.ReportBean;
import org.jeecg.modules.summary.service.ICustomerRegReportService;
import org.jeecg.modules.summary.service.PdfGeneratorService;
import org.openqa.selenium.JavascriptException;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.logging.LogType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

@Slf4j
@Service
public class PdfGeneratorServiceImpl implements PdfGeneratorService {

    @Value("${server.port}")
    private int serverPort;
    @Value("${selenium.chromePath}")
    private String chromePath;
    @Value("${selenium.chromeDriverPath}")
    private String chromeDriverPath;
    @Value("${server.servlet.context-path}")
    private String contextPath;
    @Autowired
    private ICustomerRegReportService regReportService;
    @Autowired
    private ITemplateService templateService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private ObjectMapper objectMapper; // 注入Spring配置的ObjectMapper

    @Autowired
    private WebDriverPoolManager webDriverPoolManager;

    @Autowired
    private WebDriverConcurrencyController concurrencyController;

    @Autowired
    private WebDriverExceptionHandler exceptionHandler;


    public void generatePdf() {

        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        String generateAfterReview = sysSettingService.getValueByCode("generateAfterReview");

        queryWrapper.eq(CustomerReg::getSummaryStatus, ExConstants.SUMMARY_STATUS_审核通过);
        if (StringUtils.equals(generateAfterReview, "1")) {
            queryWrapper.eq(CustomerReg::getEReportStatus, ExConstants.REPORT_STATE_待生成);
        } else {
            queryWrapper.and(i -> i.isNull(CustomerReg::getEReportStatus).or().eq(CustomerReg::getEReportStatus, ExConstants.REPORT_STATE_待审阅).or().eq(CustomerReg::getEReportStatus, ExConstants.REPORT_STATE_待生成));
        }
        queryWrapper.last("limit 100");


        List<CustomerReg> regList = customerRegMapper.selectList(queryWrapper);
        log.info("Generating PDF reports for " + regList.size() + " records...");
        if (regList.isEmpty()) {
            return;
        }

        String url = sysSettingService.getValueByCode("reportGenerateUrl");
        String localFileServerDomain = sysSettingService.getValueByCode("local_file_url");

        for (CustomerReg customerReg : regList) {
            try {
                generatePdf4CustomerReg(customerReg, url, localFileServerDomain, true);
            } catch (Exception e) {
                log.error("Failed to generate PDF report for customerRegId: {}", customerReg.getId(), e);
            }
        }
    }


    @Override
    public void generatePdf4CustomerReg(CustomerReg customerReg, String url, String localFileServerDomain, boolean updateStatus) {
        String customerRegId = customerReg.getId();
        String operationName = "generatePdf4CustomerReg_" + customerRegId;

        try {
            // 使用并发控制器执行PDF生成操作
            concurrencyController.executeWithConcurrencyControl(operationName, () -> {
                return generatePdfInternal(customerReg, url, localFileServerDomain, updateStatus);
            });

        } catch (Exception e) {
            log.error("Error generating PDF report for customerRegId: {}", customerRegId, e);
            throw new RuntimeException("Error occurred while generating PDF report: " + e.getMessage(), e);
        }
    }

    /**
     * 内部PDF生成方法
     */
    private Void generatePdfInternal(CustomerReg customerReg, String url, String localFileServerDomain, boolean updateStatus) throws Exception {
        WebDriver driver = null;
        String customerRegId = customerReg.getId();

        try {
            // 获取WebDriver
            log.info("Getting WebDriver instance for customerRegId: {}", customerRegId);
            driver = webDriverPoolManager.borrowWebDriver(url);
            JavascriptExecutor js = (JavascriptExecutor) driver;

            // 检查页面状态
            log.info("Checking page status...");
            String readyState = (String) js.executeScript("return document.readyState");
            log.info("Page status: {}", readyState);

            // 获取当前页面的基础URL，用于构建绝对路径
            String baseUrl = (String) js.executeScript(
                    "return window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1);"
            );
            log.info("Base URL: {}", baseUrl);

            // 检查脚本是否已加载
            log.info("Checking if scripts are loaded...");
            Boolean hasCore = false;
            Boolean hasPdf = false;

            // 尝试多次检查脚本加载状态
            for (int i = 0; i < 10; i++) {
                try {
                    hasCore = (Boolean) js.executeScript(
                            "return typeof MESCIUS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS.Core !== 'undefined'");

                    hasPdf = (Boolean) js.executeScript(
                            "return typeof MESCIUS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS.PdfExport !== 'undefined'");

                    if (hasCore && hasPdf) {
                        log.info("Scripts loaded successfully");
                        break;
                    }

                    log.info("Scripts not fully loaded, waiting... (Core: {}, PDF: {})", hasCore, hasPdf);
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.warn("Error checking script loading status: {}", e.getMessage());
                    Thread.sleep(1000);
                }
            }

            // 如果脚本未加载，尝试手动加载
            if (!hasCore || !hasPdf) {
                log.info("Attempting to manually load scripts...");

                // 清除现有脚本
                js.executeScript(
                        "var scripts = document.querySelectorAll('script[src*=\"ar-js\"]');" +
                                "scripts.forEach(function(script) { script.parentNode.removeChild(script); });"
                );

                // 使用绝对路径添加核心脚本
                String coreScriptUrl = baseUrl + "scripts/ar-js-core.js";
                log.info("Loading core script: {}", coreScriptUrl);
                js.executeScript(
                        "var coreScript = document.createElement('script');" +
                                "coreScript.src = arguments[0];" +
                                "document.head.appendChild(coreScript);",
                        coreScriptUrl
                );

                // 等待核心脚本加载
                log.info("Waiting for core script to load...");
                long startTime = System.currentTimeMillis();
                while (true) {
                    try {
                        hasCore = (Boolean) js.executeScript(
                                "return typeof MESCIUS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS.Core !== 'undefined'");

                        if (hasCore) {
                            log.info("Core script loaded successfully");
                            break;
                        }

                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("Core script loading timeout");
                            throw new RuntimeException("Core script loading timeout");
                        }

                        Thread.sleep(1000);
                    } catch (Exception e) {
                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("Core script loading timeout: {}", e.getMessage());
                            throw new RuntimeException("Core script loading timeout: " + e.getMessage());
                        }
                        Thread.sleep(1000);
                    }
                }

                // 使用绝对路径添加PDF脚本
                String pdfScriptUrl = baseUrl + "scripts/ar-js-pdf.js";
                log.info("Loading PDF script: {}", pdfScriptUrl);
                js.executeScript(
                        "var pdfScript = document.createElement('script');" +
                                "pdfScript.src = arguments[0];" +
                                "document.head.appendChild(pdfScript);",
                        pdfScriptUrl
                );

                // 等待PDF脚本加载
                log.info("Waiting for PDF script to load...");
                startTime = System.currentTimeMillis();
                while (true) {
                    try {
                        hasPdf = (Boolean) js.executeScript(
                                "return typeof MESCIUS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS.PdfExport !== 'undefined'");

                        if (hasPdf) {
                            log.info("PDF script loaded successfully");
                            break;
                        }

                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("PDF script loading timeout");
                            throw new RuntimeException("PDF script loading timeout");
                        }

                        Thread.sleep(1000);
                    } catch (Exception e) {
                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("PDF script loading timeout: {}", e.getMessage());
                            throw new RuntimeException("PDF script loading timeout: " + e.getMessage());
                        }
                        Thread.sleep(1000);
                    }
                }
            }

            // 注册字体 - 使用绝对路径
            log.info("Registering fonts...");
            try {
                String fontConfigUrl = baseUrl + "fontsConfig.json";
                log.info("Font config URL: {}", fontConfigUrl);
                js.executeScript(
                        "if (typeof MESCIUS !== 'undefined' && " +
                                "    typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                "    typeof MESCIUS.ActiveReportsJS.Core !== 'undefined' && " +
                                "    typeof MESCIUS.ActiveReportsJS.Core.FontStore !== 'undefined') {" +
                                "  MESCIUS.ActiveReportsJS.Core.FontStore.registerFonts(arguments[0]);" +
                                "}",
                        fontConfigUrl
                );

                // 等待字体注册
                Thread.sleep(2000);
            } catch (Exception e) {
                log.warn("Font registration may have failed: {}", e.getMessage());
            }

            // 处理单个CustomerReg的逻辑
            log.info("Preparing report data...");
            ReportBean reportBean = regReportService.getReportBean(customerRegId);
            processImageUrls(reportBean, localFileServerDomain);
            String reportBeanJson = objectMapper.writeValueAsString(reportBean);
            Template reportTemplate = getReportTemplate(customerReg);

            if (reportTemplate == null) {
                throw new RuntimeException("Report template not found");
            }

            log.info("Report template ID: {}, content length: {}",
                    reportTemplate.getId(),
                    reportTemplate.getContent() != null ? reportTemplate.getContent().length() : 0);

            // 使用直接的JavaScript代码生成PDF
            log.info("Executing PDF generation...");
            String script =
                    "var callback = arguments[arguments.length - 1];" +
                            "try {" +
                            "  console.log('Starting PDF generation...');" +
                            "  var template = JSON.parse(arguments[0]);" +
                            "  console.log('Template parsed successfully');" +
                            "  template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + arguments[1];" +
                            "  console.log('Data source configured successfully');" +
                            "  " +
                            "  console.log('Creating report instance...');" +
                            "  var report = new MESCIUS.ActiveReportsJS.Core.PageReport();" +
                            "  " +
                            "  console.log('Loading report...');" +
                            "  report.load(template)" +
                            "    .then(function() {" +
                            "      console.log('Running report...');" +
                            "      return report.run();" +
                            "    })" +
                            "    .then(function(doc) {" +
                            "      console.log('Exporting to PDF...');" +
                            "      return MESCIUS.ActiveReportsJS.PdfExport.exportDocument(doc, {" +
                            "        info: { author: 'MESCIUS' }" +
                            "      });" +
                            "    })" +
                            "    .then(function(result) {" +
                            "      console.log('Reading PDF data...');" +
                            "      var reader = new FileReader();" +
                            "      reader.readAsDataURL(result.data);" +
                            "      reader.onload = function() {" +
                            "        var base64Data = reader.result.split(',')[1];" +
                            "        console.log('PDF generated successfully, data length: ' + base64Data.length);" +
                            "        callback(base64Data);" +
                            "      };" +
                            "      reader.onerror = function(error) {" +
                            "        console.error('Failed to read PDF data', error);" +
                            "        callback('ERROR: Failed to read PDF data');" +
                            "      };" +
                            "    })" +
                            "    .catch(function(error) {" +
                            "      console.error('PDF generation failed', error);" +
                            "      callback('ERROR: ' + (error.message || error));" +
                            "    });" +
                            "} catch (error) {" +
                            "  console.error('Script execution exception', error);" +
                            "  callback('ERROR: ' + (error.message || error));" +
                            "}";

            // 获取浏览器控制台日志
            log.info("Browser console logs:");
            try {
                driver.manage().logs().get(LogType.BROWSER).getAll().forEach(
                        logEntry -> log.info("Browser: {}", logEntry.getMessage())
                );
            } catch (Exception e) {
                log.warn("Unable to get browser logs: {}", e.getMessage());
            }

            // 执行脚本
            log.info("Executing PDF generation script...");
            Object response = js.executeAsyncScript(script, reportTemplate.getContent(), reportBeanJson);

            // 再次获取浏览器控制台日志
            log.info("Browser console logs after script execution:");
            try {
                driver.manage().logs().get(LogType.BROWSER).getAll().forEach(
                        logEntry -> log.info("Browser: {}", logEntry.getMessage())
                );
            } catch (Exception e) {
                log.warn("Unable to get browser logs: {}", e.getMessage());
            }

            // 处理响应
            if (response instanceof String result) {
                if (result.startsWith("ERROR: ")) {
                    log.error("Report generation failed: {}", StringUtils.substring(result, 0, 500));
                    throw new RuntimeException("Report generation failed: " + result);
                } else {
                    log.info("PDF generated successfully, processing result...");
                    handlePdfResult(result, customerRegId, updateStatus);
                }
            } else {
                log.error("Unexpected JavaScript response: {}", response);
                throw new RuntimeException("Unexpected JavaScript response");
            }
        } catch (Exception e) {
            // 分析异常类型并执行相应的恢复操作
            WebDriverExceptionHandler.WebDriverExceptionType exceptionType =
                exceptionHandler.analyzeException(e);
            WebDriverExceptionHandler.ExceptionHandlingStrategy strategy =
                exceptionHandler.getHandlingStrategy(exceptionType);

            log.error("Error generating PDF report for customerRegId: {}, exception type: {}, strategy: {}",
                customerRegId, exceptionType, strategy, e);

            // 执行恢复操作
            if (driver != null) {
                exceptionHandler.executeRecoveryAction(driver, strategy, webDriverPoolManager);
            }

            throw new RuntimeException("Error occurred while generating PDF report: " + e.getMessage(), e);
        } finally {
            if (driver != null) {
                log.info("Returning WebDriver instance for customerRegId: {}", customerRegId);
                webDriverPoolManager.returnWebDriver(driver);
            }
        }

        return null; // Void return type
    }
    }

    @Override
    public void generatePdfByRegId(String regId, boolean updateStatus) throws Exception {
        CustomerReg customerReg = customerRegMapper.selectById(regId);

        if (customerReg != null && StringUtils.equals(customerReg.getSummaryStatus(), ExConstants.SUMMARY_STATUS_审核通过)) {
            String url = sysSettingService.getValueByCode("reportGenerateUrl");
            if (StringUtils.isBlank(url)) {
                throw new Exception("PDF report generation failed, report generation URL not configured");
            }
            String localFileServerDomain = sysSettingService.getValueByCode("local_file_url");
            if (StringUtils.isBlank(localFileServerDomain)) {
                throw new Exception("PDF report generation failed, local file server address not configured");
            }

            generatePdf4CustomerReg(customerReg, url, localFileServerDomain, updateStatus);
        } else {
            log.error("CustomerReg not found, regId: {}", regId);
            throw new Exception("Registration record not found");
        }
    }

    // 辅助方法：提取图片处理逻辑
    private void processImageUrls(ReportBean reportBean, String localFileServerDomain) {
        // 原处理图片URL的逻辑（与循环内代码相同）
        if (reportBean.getReportImgList() != null) {
            reportBean.getReportImgList().forEach(reportImg -> replaceUrlPrefix(reportImg::setText, reportImg.getText(), localFileServerDomain));
        }


        // 处理 summaryAdvice 中的图片
        if (reportBean.getSummaryAdvice() != null) {
            if (StringUtils.isNotBlank(reportBean.getSummaryAdvice().getAuditorSignPic()) && !reportBean.getSummaryAdvice().getAuditorSignPic().startsWith("http")) {
                String fullUrl = UrlUtils.concatenateUrl(localFileServerDomain, reportBean.getSummaryAdvice().getAuditorSignPic());
                reportBean.getSummaryAdvice().setAuditorSignPic(fullUrl);
            }
            if (StringUtils.isNotBlank(reportBean.getSummaryAdvice().getPreAuditorSignPic()) && !reportBean.getSummaryAdvice().getPreAuditorSignPic().startsWith("http")) {
                String fullUrl = UrlUtils.concatenateUrl(localFileServerDomain, reportBean.getSummaryAdvice().getPreAuditorSignPic());
                reportBean.getSummaryAdvice().setPreAuditorSignPic(fullUrl);
            }
            if (StringUtils.isNotBlank(reportBean.getSummaryAdvice().getCreatorSignPic()) && !reportBean.getSummaryAdvice().getCreatorSignPic().startsWith("http")) {
                String fullUrl = UrlUtils.concatenateUrl(localFileServerDomain, reportBean.getSummaryAdvice().getCreatorSignPic());
                reportBean.getSummaryAdvice().setCreatorSignPic(fullUrl);
            }
        }

    }

    // 辅助方法：替换URL前缀
    private void replaceUrlPrefix(Consumer<String> setter, String currentUrl, String domain) {
        if (StringUtils.isNotBlank(currentUrl) && !currentUrl.startsWith("http")) {
            setter.accept(UrlUtils.concatenateUrl(domain, currentUrl));
        }
    }

    // 辅助方法：获取报告模板
    private Template getReportTemplate(CustomerReg customerReg) {
        Template reportTemplate = templateService.getById(customerReg.getReportTemplateId());
        if (reportTemplate == null) {
            LambdaQueryWrapper<Template> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Template::getType, ExConstants.TEMPLATE_TYPE_报告).eq(Template::getRegType, ExConstants.BUYER_TYPE_个人).like(Template::getExamCategory, customerReg.getExamCategory());
            List<Template> templates = templateService.list(queryWrapper);
            if (!templates.isEmpty()){
                reportTemplate = templates.get(0);
            }else {
                LambdaQueryWrapper<Template> queryWrapper2 = new LambdaQueryWrapper<>();
                queryWrapper2.eq(Template::getType, ExConstants.TEMPLATE_TYPE_报告).eq(Template::getRegType, ExConstants.BUYER_TYPE_个人);
                List<Template> templates2 = templateService.list(queryWrapper);
                if (!templates2.isEmpty()){
                    reportTemplate = templates2.get(0);
                }
            }
        }
        return reportTemplate;
    }

    // 辅助方法：处理PDF结果
    private void handlePdfResult(String base64Pdf, String customerRegId, boolean updateStatus) throws Exception {
        byte[] pdfData = java.util.Base64.getDecoder().decode(base64Pdf);
        String eReportUrl = MinioUtil.upload(pdfData, "", customerRegId + ".pdf");
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CustomerReg::getEReportUrl, eReportUrl).eq(CustomerReg::getId, customerRegId);
        if (updateStatus) {
            updateWrapper.set(CustomerReg::getEReportStatus, ExConstants.REPORT_STATE_待发送);
        }
        customerRegMapper.update(null, updateWrapper);
    }

}