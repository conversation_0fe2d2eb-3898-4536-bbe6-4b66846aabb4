package org.jeecg.modules.summary.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * WebDriver进程监控服务
 * 监控Chrome进程数量，检测和清理僵尸进程
 * 
 * <AUTHOR>
 * @since 2024-08-07
 */
@Service
public class WebDriverProcessMonitor {
    private static final Logger log = LoggerFactory.getLogger(WebDriverProcessMonitor.class);
    
    // 监控配置
    @Value("${webdriver.monitor.enabled:true}")
    private boolean monitorEnabled;
    
    @Value("${webdriver.monitor.interval.minutes:5}")
    private int monitorIntervalMinutes;
    
    @Value("${webdriver.monitor.max.processes:10}")
    private int maxAllowedProcesses;
    
    @Value("${webdriver.monitor.cleanup.threshold:15}")
    private int cleanupThreshold;
    
    @Autowired
    private WebDriverPoolManager poolManager;
    
    @Autowired
    private WebDriverExceptionHandler exceptionHandler;
    
    // 监控数据
    private final AtomicInteger currentProcessCount = new AtomicInteger(0);
    private final AtomicLong totalCleanupCount = new AtomicLong(0);
    private final Map<String, ProcessInfo> processHistory = new ConcurrentHashMap<>();
    private final List<MonitoringEvent> eventHistory = Collections.synchronizedList(new ArrayList<>());
    
    // 调度器
    private ScheduledExecutorService monitorScheduler;
    private volatile boolean isShutdown = false;
    
    @PostConstruct
    public void init() {
        if (!monitorEnabled) {
            log.info("WebDriver进程监控已禁用");
            return;
        }
        
        log.info("初始化WebDriver进程监控，监控间隔: {}分钟，最大允许进程数: {}", 
            monitorIntervalMinutes, maxAllowedProcesses);
        
        monitorScheduler = Executors.newScheduledThreadPool(1, r -> {
            Thread t = new Thread(r, "WebDriverProcessMonitor");
            t.setDaemon(true);
            return t;
        });
        
        // 启动监控任务
        startMonitoring();
        
        log.info("WebDriver进程监控启动完成");
    }
    
    /**
     * 启动监控任务
     */
    private void startMonitoring() {
        monitorScheduler.scheduleWithFixedDelay(() -> {
            if (isShutdown) {
                return;
            }
            
            try {
                performMonitoringCheck();
            } catch (Exception e) {
                log.error("进程监控检查失败", e);
                recordEvent(MonitoringEventType.ERROR, "监控检查失败: " + e.getMessage());
            }
        }, 1, monitorIntervalMinutes, TimeUnit.MINUTES); // 1分钟后开始，然后按配置间隔执行
    }
    
    /**
     * 执行监控检查
     */
    private void performMonitoringCheck() {
        log.debug("开始WebDriver进程监控检查");
        
        try {
            // 获取当前Chrome进程信息
            List<ProcessInfo> currentProcesses = getCurrentChromeProcesses();
            int processCount = currentProcesses.size();
            currentProcessCount.set(processCount);
            
            // 更新进程历史记录
            updateProcessHistory(currentProcesses);
            
            // 记录监控事件
            recordEvent(MonitoringEventType.CHECK, 
                String.format("检测到%d个Chrome进程", processCount));
            
            log.debug("当前Chrome进程数: {}, 池状态: {}", processCount, poolManager.getPoolStatus());
            
            // 检查是否需要清理
            if (processCount > maxAllowedProcesses) {
                log.warn("Chrome进程数量超过阈值: {} > {}", processCount, maxAllowedProcesses);
                recordEvent(MonitoringEventType.WARNING, 
                    String.format("进程数量超过阈值: %d > %d", processCount, maxAllowedProcesses));
                
                if (processCount >= cleanupThreshold) {
                    log.error("Chrome进程数量达到清理阈值: {} >= {}, 执行强制清理", 
                        processCount, cleanupThreshold);
                    performEmergencyCleanup();
                }
            }
            
            // 检测僵尸进程
            detectZombieProcesses(currentProcesses);
            
            // 清理过期的历史记录
            cleanupOldHistory();
            
        } catch (Exception e) {
            log.error("进程监控检查过程中发生异常", e);
            recordEvent(MonitoringEventType.ERROR, "监控异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前Chrome进程信息
     */
    private List<ProcessInfo> getCurrentChromeProcesses() {
        List<ProcessInfo> processes = new ArrayList<>();
        
        try {
            String os = System.getProperty("os.name").toLowerCase();
            String command;
            
            if (os.contains("win")) {
                command = "tasklist /FI \"IMAGENAME eq chrome.exe\" /FO CSV";
            } else {
                command = "ps aux | grep chrome | grep -v grep";
            }
            
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            String line;
            while ((line = reader.readLine()) != null) {
                ProcessInfo processInfo = parseProcessLine(line, os.contains("win"));
                if (processInfo != null) {
                    processes.add(processInfo);
                }
            }
            
            reader.close();
            process.waitFor(10, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            log.debug("获取Chrome进程信息失败", e);
        }
        
        return processes;
    }
    
    /**
     * 解析进程信息行
     */
    private ProcessInfo parseProcessLine(String line, boolean isWindows) {
        try {
            if (isWindows) {
                // Windows CSV格式: "Image Name","PID","Session Name","Session#","Mem Usage"
                if (line.toLowerCase().contains("chrome.exe")) {
                    String[] parts = line.split(",");
                    if (parts.length >= 5) {
                        String pid = parts[1].replace("\"", "").trim();
                        String memory = parts[4].replace("\"", "").trim();
                        return new ProcessInfo(pid, memory, LocalDateTime.now());
                    }
                }
            } else {
                // Linux格式: USER PID %CPU %MEM VSZ RSS TTY STAT START TIME COMMAND
                if (line.toLowerCase().contains("chrome") && !line.contains("grep")) {
                    String[] parts = line.trim().split("\\s+");
                    if (parts.length >= 11) {
                        String pid = parts[1];
                        String memory = parts[5]; // RSS
                        return new ProcessInfo(pid, memory, LocalDateTime.now());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("解析进程信息失败: {}", line, e);
        }
        
        return null;
    }
    
    /**
     * 更新进程历史记录
     */
    private void updateProcessHistory(List<ProcessInfo> currentProcesses) {
        // 清理不存在的进程
        Set<String> currentPids = new HashSet<>();
        for (ProcessInfo process : currentProcesses) {
            currentPids.add(process.getPid());
        }
        
        processHistory.entrySet().removeIf(entry -> !currentPids.contains(entry.getKey()));
        
        // 添加新进程
        for (ProcessInfo process : currentProcesses) {
            processHistory.put(process.getPid(), process);
        }
    }
    
    /**
     * 检测僵尸进程
     */
    private void detectZombieProcesses(List<ProcessInfo> currentProcesses) {
        // 简单的僵尸进程检测：运行时间过长的进程
        LocalDateTime threshold = LocalDateTime.now().minusHours(2);
        
        for (ProcessInfo process : currentProcesses) {
            ProcessInfo historical = processHistory.get(process.getPid());
            if (historical != null && historical.getStartTime().isBefore(threshold)) {
                log.warn("检测到可能的僵尸Chrome进程: PID={}, 运行时间超过2小时", process.getPid());
                recordEvent(MonitoringEventType.WARNING, 
                    String.format("可能的僵尸进程: PID=%s", process.getPid()));
            }
        }
    }
    
    /**
     * 执行紧急清理
     */
    private void performEmergencyCleanup() {
        log.warn("执行Chrome进程紧急清理");
        recordEvent(MonitoringEventType.CLEANUP, "开始紧急清理");
        
        try {
            // 使用WebDriverPoolManager的强制清理功能
            poolManager.forceCleanupAllChromeProcesses();
            totalCleanupCount.incrementAndGet();
            
            // 等待一段时间后重新检查
            Thread.sleep(5000);
            
            List<ProcessInfo> remainingProcesses = getCurrentChromeProcesses();
            int remainingCount = remainingProcesses.size();
            
            log.info("紧急清理完成，剩余Chrome进程数: {}", remainingCount);
            recordEvent(MonitoringEventType.CLEANUP, 
                String.format("紧急清理完成，剩余进程: %d", remainingCount));
            
        } catch (Exception e) {
            log.error("紧急清理失败", e);
            recordEvent(MonitoringEventType.ERROR, "紧急清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录监控事件
     */
    private void recordEvent(MonitoringEventType type, String message) {
        MonitoringEvent event = new MonitoringEvent(type, message, LocalDateTime.now());
        eventHistory.add(event);
        
        // 限制事件历史记录数量
        if (eventHistory.size() > 1000) {
            eventHistory.subList(0, 500).clear(); // 保留最近500条记录
        }
    }
    
    /**
     * 清理过期的历史记录
     */
    private void cleanupOldHistory() {
        LocalDateTime cutoff = LocalDateTime.now().minusDays(1);
        
        // 清理过期的进程历史
        processHistory.entrySet().removeIf(entry -> 
            entry.getValue().getStartTime().isBefore(cutoff));
        
        // 清理过期的事件历史
        eventHistory.removeIf(event -> event.getTimestamp().isBefore(cutoff));
    }
    
    /**
     * 获取监控状态报告
     */
    public MonitoringReport getMonitoringReport() {
        return new MonitoringReport(
            currentProcessCount.get(),
            totalCleanupCount.get(),
            processHistory.size(),
            eventHistory.size(),
            poolManager.getPoolStatus(),
            LocalDateTime.now()
        );
    }
    
    /**
     * 获取最近的监控事件
     */
    public List<MonitoringEvent> getRecentEvents(int limit) {
        synchronized (eventHistory) {
            int size = eventHistory.size();
            int fromIndex = Math.max(0, size - limit);
            return new ArrayList<>(eventHistory.subList(fromIndex, size));
        }
    }
    
    @PreDestroy
    public void shutdown() {
        log.info("关闭WebDriver进程监控");
        isShutdown = true;
        
        if (monitorScheduler != null && !monitorScheduler.isShutdown()) {
            monitorScheduler.shutdown();
            try {
                if (!monitorScheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    monitorScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitorScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("WebDriver进程监控已关闭");
    }
    
    /**
     * 进程信息类
     */
    public static class ProcessInfo {
        private final String pid;
        private final String memory;
        private final LocalDateTime startTime;
        
        public ProcessInfo(String pid, String memory, LocalDateTime startTime) {
            this.pid = pid;
            this.memory = memory;
            this.startTime = startTime;
        }
        
        public String getPid() { return pid; }
        public String getMemory() { return memory; }
        public LocalDateTime getStartTime() { return startTime; }
    }
    
    /**
     * 监控事件类
     */
    public static class MonitoringEvent {
        private final MonitoringEventType type;
        private final String message;
        private final LocalDateTime timestamp;
        
        public MonitoringEvent(MonitoringEventType type, String message, LocalDateTime timestamp) {
            this.type = type;
            this.message = message;
            this.timestamp = timestamp;
        }
        
        public MonitoringEventType getType() { return type; }
        public String getMessage() { return message; }
        public LocalDateTime getTimestamp() { return timestamp; }
        
        @Override
        public String toString() {
            return String.format("[%s] %s: %s", 
                timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), 
                type, message);
        }
    }
    
    /**
     * 监控事件类型
     */
    public enum MonitoringEventType {
        CHECK, WARNING, ERROR, CLEANUP
    }
    
    /**
     * 监控报告类
     */
    public static class MonitoringReport {
        private final int currentProcessCount;
        private final long totalCleanupCount;
        private final int processHistorySize;
        private final int eventHistorySize;
        private final String poolStatus;
        private final LocalDateTime reportTime;
        
        public MonitoringReport(int currentProcessCount, long totalCleanupCount, 
                              int processHistorySize, int eventHistorySize, 
                              String poolStatus, LocalDateTime reportTime) {
            this.currentProcessCount = currentProcessCount;
            this.totalCleanupCount = totalCleanupCount;
            this.processHistorySize = processHistorySize;
            this.eventHistorySize = eventHistorySize;
            this.poolStatus = poolStatus;
            this.reportTime = reportTime;
        }
        
        public int getCurrentProcessCount() { return currentProcessCount; }
        public long getTotalCleanupCount() { return totalCleanupCount; }
        public int getProcessHistorySize() { return processHistorySize; }
        public int getEventHistorySize() { return eventHistorySize; }
        public String getPoolStatus() { return poolStatus; }
        public LocalDateTime getReportTime() { return reportTime; }
        
        @Override
        public String toString() {
            return String.format("监控报告 [%s] - 当前进程: %d, 总清理次数: %d, 进程历史: %d, 事件历史: %d, 池状态: %s",
                reportTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                currentProcessCount, totalCleanupCount, processHistorySize, eventHistorySize, poolStatus);
        }
    }
}
