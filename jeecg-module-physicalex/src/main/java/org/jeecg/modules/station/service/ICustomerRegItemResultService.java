package org.jeecg.modules.station.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.summary.bo.DepartAndGroupBean;

import java.util.List;

/**
 * @Description: 体检小项结果表
 * @Author: jeecg-boot
 * @Date: 2024-04-21
 * @Version: V1.0
 */
public interface ICustomerRegItemResultService extends IService<CustomerRegItemResult> {

    void fetchCheckData(String customerRegId, String startTime, String endTime) throws Exception;

    public void fetchLisData(String customerRegId, String startTime, String endTime) throws Exception;

    List<CustomerRegItemResult> saveItemResult(List<CustomerRegItemResult> customerRegItemResultList, CustomerReg reg, Boolean isTempSave);

    CustomerRegItemResult saveItemResult(CustomerRegItemResult customerRegItemResult);

    CustomerRegItemResult abandonItemResult(CustomerRegItemResult customerRegItemResult);

    void abandonGroupBatch(String customerRegId, List<String> itemGroupIds);

    void unAbandonGroupBatch(String customerRegId, List<String> itemGroupIds);

    CustomerRegItemResult unAbandonItemResult(CustomerRegItemResult customerRegItemResult);

    void abandonGroup(String itemGroupId, String customerRegId);

    void unAbandonGroup(String itemGroupId, String customerRegId);

    List<CustomerRegItemGroup> listGroupByRegId(String regId, List<String> departmentIds);

    List<CustomerRegItemGroup> listGroupByRegId(String regId, List<String> departmentIds, boolean includeDependencies);

    boolean isAllItemAbandoned(String itemGroupId, String customerRegId);

    ItemStandard findMatchedItemStandard(CustomerRegItemResult itemResult, CustomerReg reg);

    List<CustomerRegItemResult> listByRegId(String regId);

    List<CustomerRegItemResult> listKeyResultByIdCard(String idCard, Integer year);

    List<CustomerRegItemResult> listHistoryResultByIdCard(String idCard, Integer year);

    List<String> listHistoryAbnormaoResultIdsByIdCard(String idCard, Integer year);

    void resetCheckStatusBatch(String customerRegId, List<String> itemGroupIds);

    void setStatusCheckedtusBatch(String customerRegId, List<String> itemGroupIds);

    boolean sendCheckNotifyBatch(String customerRegId, List<String> itemGroupNames);

    List<CustomerRegItemResult> listAll();

    List<DepartAndGroupBean> listDepartAndGroup(String regId);

    void clearGroupResult(String itemGroupId, String customerRegId,String departmentId);

    List<StatusStat> statCheckStatusByRegId(String regId);

    List<CustomerRegItemGroup> listUncheckGroupByRegId(String regId);

    List<CustomerRegItemGroup> getAbnormalItemGroupList(String customerRegId);
}
