package org.jeecg.modules.reg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.reg.bo.PriceStat;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.interfacecheck.entity.InterfaceResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: customer_reg_item_group
 * @Author: jeecg-boot
 * @Date: 2024-04-03
 * @Version: V1.0
 */
public interface CustomerRegItemGroupMapper extends BaseMapper<CustomerRegItemGroup> {

    void updateItemGroupAbnormalFlagBatch(@Param("regId") String regId, @Param("itemGroupIds") List<String> itemGroupIds, @Param("abnormalFlag") String abnormalFlag);

    void updateItemGroupCheckStatusBatch(@Param("regId") String regId, @Param("itemGroupIds") List<String> itemGroupIds, @Param("status") String status, @Param("statusLimit") String statusLimit, @Param("checkDoctorCode") String checkDoctorCode, @Param("checkDoctorName") String checkDoctorName);

    /**
     * 批量更新项目组异常标志（支持部位）
     * @param regId 登记ID
     * @param itemGroupIds 项目组ID列表
     * @param checkPartCodes 检查部位编码列表（与itemGroupIds一一对应）
     * @param abnormalFlag 异常标志
     */
    void updateItemGroupAbnormalFlagBatchWithPart(@Param("regId") String regId, @Param("itemGroupIds") List<String> itemGroupIds, @Param("checkPartCodes") List<String> checkPartCodes, @Param("abnormalFlag") String abnormalFlag);

    /**
     * 批量更新项目组检查状态（支持部位）
     * @param regId 登记ID
     * @param itemGroupIds 项目组ID列表
     * @param checkPartCodes 检查部位编码列表（与itemGroupIds一一对应）
     * @param status 检查状态
     * @param statusLimit 状态限制条件
     * @param checkDoctorCode 检查医生代码
     * @param checkDoctorName 检查医生姓名
     */
    void updateItemGroupCheckStatusBatchWithPart(@Param("regId") String regId, @Param("itemGroupIds") List<String> itemGroupIds, @Param("checkPartCodes") List<String> checkPartCodes, @Param("status") String status, @Param("statusLimit") String statusLimit, @Param("checkDoctorCode") String checkDoctorCode, @Param("checkDoctorName") String checkDoctorName);

    List<StatusStat> statCustomerRegItemGroupStatus(@Param("regId") String regId, @Param("departmentIds") List<String> departmentIds);

    List<StatusStat> statCustomerRegItemGroupInterfaceStatus(@Param("regId") String regId, @Param("departmentIds") List<String> departmentIds);

    StatusStat statCustomerRegItemGroupAbandoned(@Param("regId") String regId, @Param("departmentIds") List<String> departmentIds);

    List<CustomerRegItemGroup> listByRegAndDepart(@Param("customerRegId") String customerRegId, @Param("departmentIds") List<String> departmentIds);

    List<CustomerRegItemGroup> listByReg(@Param("customerRegId") String customerRegId, @Param("payStatus") String payStatus);

    List<CustomerRegItemGroup> listWithItemGroupByReg(@Param("customerRegId") String customerRegId, @Param("departmentIds") List<String> departmentIds, @Param("containFeeOnly") Boolean containFeeOnly);

    String getCheckConclusion(@Param("id") String id);

    CustomerRegItemGroup getByRegAndGroup(@Param("regId") String regId, @Param("groupId") String groupId);

    PriceStat statPrice4Reg(@Param("regId") String regId);

    List<CustomerRegItemGroup> listByCompanyRegId(@Param("companyRegId") String companyRegId);

    List<CustomerRegItemGroup> listByIds(@Param("ids") List<String> ids);

    void updatePayStatusByIds(@Param("ids") List<String> ids, @Param("status") String status, @Param("statusLimit") String statusLimit);

    List<CustomerRegItemGroup> listByFeeRecordId(@Param("feeRecordId") String feeRecordId);

    BigDecimal getTotalAmount(@Param("ids") List<String> ids);

    List<DepartStat> checkStatGroupByDepart(@Param("customerRegId") String customerRegId);

    List<DepartStat> regGroupsGroupByDepart(@Param("customerRegId") String customerRegId);

    List<CustomerRegItemGroup> listByBillId(@Param("billId") String billId);

    List<String> listIdByBillId(@Param("billId") String billId);

    List<CustomerRegItemGroup> listLiteByRegId(@Param("customerRegId") String regId);

    InterfaceResult getInterfaceResultByRegGroupId(@Param("regGroupId") String regGroupId);

    List<CustomerRegItemGroup> getGroupsByRegIdAndDepartId(@Param("regId") String regId, @Param("departmentId") String departmentId, @Param("checkStatus") String checkStatus);

    CustomerRegItemGroup getGroupByPsySuitIdAndRegId(@Param("psySuitId") String psySuitId,@Param("regId") String regId );


    List<CustomerRegItemGroup> listWithItemGroupByRegIds(@Param("regIds") List<String> regIds,  @Param("containFeeOnly") Boolean containFeeOnly);

    List<ItemGroupRelation> getRelationGroupByRegIdAndGroupId(@Param("regId")String regId, @Param("groupId")String groupId);

}
