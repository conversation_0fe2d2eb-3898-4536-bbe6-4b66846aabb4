#!/bin/bash

# WebDriverPool系统监控脚本
# 用于压力测试期间监控系统资源使用情况

# 配置参数
LOG_DIR="./logs"
LOG_FILE="$LOG_DIR/system_monitor_$(date +%Y%m%d_%H%M%S).log"
INTERVAL=30  # 监控间隔（秒）
CHROME_THRESHOLD=10  # Chrome进程数量告警阈值

# 创建日志目录
mkdir -p $LOG_DIR

# 初始化日志文件
echo "WebDriverPool系统监控开始 - $(date)" > $LOG_FILE
echo "监控间隔: ${INTERVAL}秒" >> $LOG_FILE
echo "Chrome进程告警阈值: ${CHROME_THRESHOLD}个" >> $LOG_FILE
echo "========================================" >> $LOG_FILE

# 监控函数
monitor_system() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "" >> $LOG_FILE
    echo "=== $timestamp ===" >> $LOG_FILE
    
    # 1. CPU和内存使用情况
    echo "--- CPU和内存使用 ---" >> $LOG_FILE
    top -bn1 | head -5 >> $LOG_FILE
    
    # 2. 内存详细信息
    echo "--- 内存详细信息 ---" >> $LOG_FILE
    free -h >> $LOG_FILE
    
    # 3. Chrome进程监控
    echo "--- Chrome进程监控 ---" >> $LOG_FILE
    local chrome_count=$(ps aux | grep -i chrome | grep -v grep | wc -l)
    echo "Chrome进程总数: $chrome_count" >> $LOG_FILE
    
    if [ $chrome_count -gt $CHROME_THRESHOLD ]; then
        echo "⚠️  警告: Chrome进程数量超过阈值! ($chrome_count > $CHROME_THRESHOLD)" >> $LOG_FILE
        echo "Chrome进程详情:" >> $LOG_FILE
        ps aux | grep -i chrome | grep -v grep >> $LOG_FILE
    fi
    
    # 4. Chrome内存使用统计
    local chrome_memory=$(ps aux | grep -i chrome | grep -v grep | awk '{sum+=$6} END {if(sum) print sum/1024 " MB"; else print "0 MB"}')
    echo "Chrome总内存使用: $chrome_memory" >> $LOG_FILE
    
    # 5. 磁盘使用情况
    echo "--- 磁盘使用情况 ---" >> $LOG_FILE
    df -h | grep -E "(Filesystem|/dev/)" >> $LOG_FILE
    
    # 6. 网络连接统计
    echo "--- 网络连接统计 ---" >> $LOG_FILE
    netstat -an | grep :8080 | wc -l | xargs echo "端口8080连接数:" >> $LOG_FILE
    
    # 7. Java进程监控
    echo "--- Java进程监控 ---" >> $LOG_FILE
    local java_pid=$(pgrep -f "jeecg-boot")
    if [ ! -z "$java_pid" ]; then
        echo "Java进程PID: $java_pid" >> $LOG_FILE
        ps -p $java_pid -o pid,ppid,cmd,%mem,%cpu >> $LOG_FILE
        
        # JVM内存使用情况（如果jstat可用）
        if command -v jstat &> /dev/null; then
            echo "JVM内存使用:" >> $LOG_FILE
            jstat -gc $java_pid >> $LOG_FILE 2>/dev/null || echo "无法获取JVM内存信息" >> $LOG_FILE
        fi
    else
        echo "未找到Java进程" >> $LOG_FILE
    fi
    
    # 8. 负载平均值
    echo "--- 系统负载 ---" >> $LOG_FILE
    uptime >> $LOG_FILE
}

# 监控WebDriver池状态的函数
monitor_webdriver_pool() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "--- WebDriver池状态监控 ($timestamp) ---" >> $LOG_FILE
    
    # 通过日志文件监控WebDriver池状态（如果有的话）
    local app_log="/path/to/application.log"  # 需要根据实际路径调整
    
    if [ -f "$app_log" ]; then
        # 查找最近的WebDriver池状态日志
        tail -n 1000 "$app_log" | grep -i "webdriver.*pool.*status" | tail -5 >> $LOG_FILE 2>/dev/null
        
        # 查找最近的异常日志
        tail -n 1000 "$app_log" | grep -i "error.*webdriver" | tail -3 >> $LOG_FILE 2>/dev/null
    else
        echo "应用日志文件不存在: $app_log" >> $LOG_FILE
    fi
}

# 生成监控报告
generate_report() {
    local report_file="$LOG_DIR/monitor_report_$(date +%Y%m%d_%H%M%S).txt"
    
    echo "生成监控报告: $report_file"
    
    {
        echo "WebDriverPool压力测试监控报告"
        echo "生成时间: $(date)"
        echo "=========================================="
        echo ""
        
        echo "1. Chrome进程数量统计:"
        grep "Chrome进程总数:" $LOG_FILE | tail -10
        echo ""
        
        echo "2. 内存使用趋势:"
        grep -A1 "内存详细信息" $LOG_FILE | grep "Mem:" | tail -10
        echo ""
        
        echo "3. 系统负载趋势:"
        grep "load average" $LOG_FILE | tail -10
        echo ""
        
        echo "4. 告警信息:"
        grep "⚠️" $LOG_FILE || echo "无告警信息"
        echo ""
        
        echo "5. 异常信息:"
        grep -i "error\|exception\|failed" $LOG_FILE | tail -10 || echo "无异常信息"
        
    } > $report_file
    
    echo "监控报告已生成: $report_file"
}

# 清理函数
cleanup() {
    echo ""
    echo "监控停止 - $(date)" >> $LOG_FILE
    generate_report
    echo "监控已停止，日志文件: $LOG_FILE"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主监控循环
echo "开始系统监控，日志文件: $LOG_FILE"
echo "按 Ctrl+C 停止监控"

while true; do
    monitor_system
    monitor_webdriver_pool
    
    # 检查日志文件大小，如果超过100MB则轮转
    if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -gt 104857600 ]; then
        mv "$LOG_FILE" "${LOG_FILE}.$(date +%H%M%S)"
        echo "WebDriverPool系统监控继续 - $(date)" > $LOG_FILE
    fi
    
    sleep $INTERVAL
done
