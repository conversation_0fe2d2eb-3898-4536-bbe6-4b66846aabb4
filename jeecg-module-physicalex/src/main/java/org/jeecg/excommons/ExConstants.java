package org.jeecg.excommons;

public class ExConstants {

    public static final String SEQ_REG_SERIAL_NO = "regSerialNo";
    public static final String SEQ_APPOINTMENT_NO = "appointmentNo";
    public static final String SEQ_EXAMNO = "examNo";
    public static final String SEQ_BARCODE_NO = "barcodeNo";
    public static final String SEQ_RECIPE_NO = "recipeNo";
    public static final String SEQ_REFUND_BILL_NO = "refundBillNo";
    public static final String SEQ_COMPANY_REPORT_NO = "companyReportNo";

    public static final String SEQ_ARCHIVE_NO = "archiveNo";
    public static final String PAY_STATUS_PAYED = "已支付";

    public static final String PAY_STATUS_PART = "部分支付";
    public static final String PAY_STATUS_WAIT = "待支付";
    public static final String REG_STATUS_WAIT = "未登记";
    public static final String REG_STATUS_REGED = "已登记";
    public static final String INTERFACE_SYNC_UN_REGED = "取消登记";
    public static final String INTERFACE_SYNC_STATUS_WAIT = "待同步";
    public static final String INTERFACE_SYNC_CANCELED = "取消登记";
    public static final String INTERFACE_SYNC_SAMPLED = "已采样";
    public static final String ITEM_RESULT_STATUS_放弃 = "放弃";
    public static final String ITEM_RESULT_STATUS_已检 = "已检";

    //public static final String CHECK_STATUS_待查 = "待查";
    public static final String CHECK_STATUS_已检 = "已检";

    public static final String CHECK_STATUS_放弃 = "放弃";

    public static final String CHECK_STATUS_未检 = "未检";

    public static final String CHECK_STATUS_已小结 = "已小结";

    public static final String CHECK_STATUS_总检 = "总检";

    public static final String CHECK_STATUS_报告打印 = "报告打印";

    public static final Integer ADD_MINUS_FLAG_MINUS = -1;
    public static final Integer ADD_MINUS_FLAG_Add = 1;
    public static final Integer ADD_MINUS_FLAG_NORMAL = 0;

    public static final String ITEM_SYMBO_正常 = "正常";

    public static final String ITEM_VALUE_TYPE_数值型 = "数值型";

    public static final String ITEM_VALUE_TYPE_说明型 = "说明型";

    public static final String ITEM_VALUE_TYPE_计算型 = "计算型";

    public static final String OPERATOR_大于 = "大于";
    public static final String OPERATOR_大于等于 = "大于等于";
    public static final String OPERATOR_小于 = "小于";
    public static final String OPERATOR_小于等于 = "小于等于";
    public static final String OPERATOR_等于 = "等于";
    public static final String OPERATOR_不等于 = "不等于";

    public static final String OPERATOR_包含 = "包含";

    public static final String OPERATOR_不包含 = "不包含";

    public static final String OPERATOR_正则 = "正则";

    public static final String SUMMARY_NORMAL_TEXT = "未见明显异常";

    public static final String SEVERITY_DEGREE_A类 = "A类";

    public static final String SEVERITY_DEGREE_B类 = "B类";

    public static final String TRIGGER_TYPE_复合判断 = "复合判断";

    public static final String TRIGGER_TYPE_项目参考值 = "项目参考值";

    public static final String TRIGGER_SOURCE_科室小结 = "科室小结";

    public static final String TRIGGER_SOURCE_接口通知 = "接口通知";

    public static final String TRIGGER_SOURCE_总检 = "总检";

    public static final String LIMIT_不限 = "不限";

    public static final String ITEM_SYMBO_高 = "↑";

    public static final String ITEM_SYMBO_低 = "↓";

    public static final String PROGRESS_STATUS_暂存 = "暂存";

    public static final String PROGRESS_STATUS_完成 = "完成";

    public static final String CRITICAL_DEAL_ACTION_CONFIRM = "确认";

    public static final String CRITICAL_DEAL_ACTION_NOTIFY = "通知";

    public static final String CRITICAL_DEAL_ACTION_VISIT = "回访";

    public static final String CRITICAL_DEAL_ACTION_CONIRM_RESET = "重置确认状态";

    public static final String CRITICAL_DEAL_ACTION_NOTIFY_RESET = "重置通知状态";

    public static final String CRITICAL_DEAL_ACTION_VISIT_RESET = "重置回访状态";

    //总检状态
    public static final String SUMMARY_STATUS_已预检 = "已预检";
    public static final String SUMMARY_STATUS_已初检 = "已初检";
    public static final String SUMMARY_STATUS_已总检 = "已总检";
    public static final String SUMMARY_STATUS_未总检 = "未总检";
    public static final String SUMMARY_STATUS_已初审 = "已初审";
    public static final String SUMMARY_STATUS_未审核 = "未审核";
    public static final String SUMMARY_STATUS_审核通过 = "审核通过";
    public static final String SUMMARY_STATUS_驳回 = "驳回";
    public static final String REPORT_STATUS_未打印 = "未打印";
    public static final String REPORT_STATUS_已打印 = "已打印";
    public static final String REPORT_STATUS_已取走 = "已取走";
    public static final String REPORT_STATUS_已召回 = "已召回";

    public static final String PRE_SUMMARY_TYPE_手动 = "手动";

    public static final String PRE_SUMMARY_TYPE_自动 = "自动";

    public static final String INITAIL_SUMMARY_TYPE_手动 = "手动";

    public static final String INITAIL_SUMMARY_TYPE_AI = "AI";

    //条码号来源
    public static final String BARCODE_NUM_SOURCE_自动生成 = "自动生成";
    public static final String BARCODE_NUM_SOURCE_体检号 = "体检号";

    //模版类型
    public static final String TEMPLATE_TYPE_报告 = "报告";
    public static final String TEMPLATE_TYPE_导引单 = "导引单";
    public static final String TEMPLATE_TYPE_申请单 = "申请单";

    //支付方
    public static final String PAYER_TYPE_个人支付 = "个人支付";
    public static final String PAYER_TYPE_单位支付 = "单位支付";

    //支付记录状态 支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭
    public static final String PAY_STATE_订单生成 = "订单生成";
    public static final String PAY_STATE_支付中 = "支付中";
    public static final String PAY_STATE_待支付 = "待支付";
    public static final String PAY_STATE_支付成功 = "支付成功";
    public static final String PAY_STATE_支付失败 = "支付失败";
    public static final String PAY_STATE_已撤销 = "已撤销";
    public static final String PAY_STATE_退款成功 = "退款成功";
    public static final String PAY_STATE_订单关闭 = "订单关闭";

    public static final String PAY_STATE_已支付 = "已支付";


    //退款状态:0-订单生成,1-退款中,2-退款成功,3-退款失败,4-退款任务关闭
    public static final String REFUND_STATE_订单生成 = "订单生成";
    public static final String REFUND_STATE_退款中 = "退款中";
    public static final String REFUND_STATE_退款成功 = "退款成功";
    public static final String REFUND_STATE_退款失败 = "退款失败";
    public static final String REFUND_STATE_退款任务关闭 = "退款任务关闭";
    public static final String REFUND_STATE_已撤销 = "已撤销";

    //支付方式
    public static final String PAY_CHANNEL_门诊 = "门诊";

    public static final String PAY_CHANNEL_现金 = "现金";

    public static final String PAY_CHANNEL_凭证支付 = "凭证支付";

    public static final String PAY_CHANNEL_组合支付 = "组合支付";

    public static final String PAY_CHANNEL_微信支付 = "微信支付";

    public static final String PAY_CHANNEL_支付宝支付 = "付宝支付";

    public static final String PAY_CHANNEL_聚合支付 = "聚合支付";

    public static final String PAY_CHANNEL_体检卡 = "体检卡";

    public static final String PAY_CHANNEL_赠送 = "赠送";

    public static final String PAY_BIZ_TYPE_体检费 = "体检费";

    public static final String PAY_BIZ_TYPE_体检卡重置 = "体检卡重置";

    public static final String PAY_BIZ_TYPE_体检卡费 = "体检卡费";
    public static final String PAY_BIZ_TYPE_作废退费单 = "作废退费单";

    public static final String PAY_CHANNEL_单位支付 = "单位支付";
    public static final String PAY_CHANNEL_先检后付 = "先检后付";

    public static final String BARCODE_TEMPLATE_LIS = "LIS";

    public static final String BARCODE_TEMPLATE_FEE = "FEE";

    public static final String PRINT_STATUS_未打印 = "未打印";

    public static final String PRINT_STATUS_已打印 = "已打印";

    public static final String RECORD_CHANGE_APPLY_STATUS_APPLIED = "待确认";

    public static final String RECORD_CHANGE_APPLY_STATUS_AGREED = "已同意";

    public static final String RECORD_CHANGE_APPLY_STATUS_REFUSED = "已拒绝";

    public static final String RECORD_CHANGE_APPLY_STATUS_COMPLISHED = "已完成";

    public static final String PROCESS_BUSINESS_TYPE_体检数据修改 = "体检数据修改";

    public static final String ENC_SECRET_KEY = "V9T5z/MtYevKonjiQtlQ+FUzacJAS2+b5zb13WEm9Xs=";

    public static final String CRAD_STATUS_待写入 = "待写入";
    public static final String CRAD_STATUS_待发行 = "待发行";
    public static final String CRAD_STATUS_已发行 = "已发行";
    public static final String CRAD_STATUS_已激活 = "已激活";
    public static final String CRAD_STATUS_已作废 = "已作废";
    public static final String CRAD_STATUS_冻结中 = "冻结中";
    public static final String CRAD_STATUS_已锁定 = "已锁定";
    public static final String CRAD_SOURCE_新发 = "新发";
    public static final String CRAD_SOURCE_换发 = "换发";

    public static final String CRAD_ORDER_STATUS_待支付 = "待支付";
    public static final String CRAD_ORDER_STATUS_支付中 = "支付中";
    public static final String CRAD_ORDER_STATUS_已支付 = "已支付";
    public static final String CRAD_ORDER_STATUS_待发货 = "待发货";
    public static final String CRAD_ORDER_STATUS_已收货 = "已收货";
    public static final String CRAD_ORDER_STATUS_已取消 = "已取消";
    public static final String CRAD_ORDER_STATUS_已作废 = "已作废";

    public static final String BUYER_TYPE_单位 = "单位";

    public static final String BUYER_TYPE_个人 = "个人";

    public static final String CARD_TRADE_TYPE_入 = "入";
    public static final String CARD_TRADE_TYPE_出 = "出";

    public static final String CARD_TRADE_STATUS_成功 = "成功";

    public static final String CARD_TRADE_STATUS_失败 = "失败";

    //短信发送方式：立即，定时
    public static final String SMS_SEND_METHOD_IMMEDIATELY = "立即";
    public static final String SMS_SEND_METHOD_TIMING = "定时";

    //SMS_STATE_SUCCESS
    public static final String SMS_STATE_SUCCESS = "成功";
    public static final String SMS_STATE_FAIL = "失败";

    //SMS_BIZ_TYPE_CRITICAL
    public static final String SMS_BIZ_TYPE_危急值通知 = "危急值通知";
    public static final String SMS_BIZ_TYPE_回访 = "回访";
    public static final String SMS_BIZ_TYPE_报告通知 = "报告通知";
    public static final String SMS_BIZ_TYPE_团检通知 = "团检通知";
    public static final String SMS_BIZ_TYPE_复查提醒 = "复查提醒";
    public static final String SMS_BIZ_TYPE_团检接洽 = "团检接洽";

    //报告状态
    public static final String REPORT_STATE_待生成 = "待生成";
    public static final String REPORT_STATE_待审阅 = "待审阅";
    public static final String REPORT_STATE_待发送 = "待发送";
    public static final String REPORT_STATE_已发送 = "已发送";
    public static final String REPORT_STATE_已阅览 = "已阅览";
    public static final String REPORT_STATE_已召回 = "已召回";
    public static final String REPORT_STATE_已打印 = "已打印";
    public static final String REPORT_STATE_已取走 = "已取走";

    public static final String COMPANY_NOTIFY_待发送 = "待发送";

    public static final String COMPANY_NOTIFY_已发送 = "已发送";

    public static final String ORDER_STATUS_PAYED = "PAYED";
    public static final String ORDER_STATUS_CANCELED = "CANCELED";
    public static final String ORDER_STATUS_SUBMITTED = "SUBMITTED";
    public static final String ORDER_STATUS_REPORTED = "REPORTED";

    public static final String ORDER_STATUS_待支付 = "待支付";
    public static final String ORDER_STATUS_待登记 = "待登记";
    public static final String ORDER_STATUS_已登记= "已登记";
    public static final String ORDER_STATUS_已取消 = "已取消";


    //AI模块
    public static final String AI_MOD_DEEPSEEK = "DeepSeek";
    public static final String AI_MOD_BAICHUAN = "baichuan";
    public static final String AI_MOD_DASHSCOPE = "dashscope";

    public static final String AI_MODULE_总检 = "总检";
    public static final String AI_MODULE_基础信息 = "基础信息";
    public static final String AI_MODULE_选项 = "选项";
    public static final String AI_MODULE_心理 = "心理";

    public static final String AI_FUNC_选项 = "选项";
    public static final String AI_FUNC_聊天 = "聊天";
    public static final String AI_FUNC_总检建议 = "总检建议";
    public static final String AI_FUNC_总检建议字典 = "总检建议字典";
    public static final String AI_FUNC_汇总建议 = "汇总建议";
    public static final String AI_FUNC_健康建议 = "健康建议";
    public static final String AI_FUNC_慢病分组 = "慢病分组";
    public static final String AI_FUNC_就诊建议 = "就诊建议";
    public static final String AI_FUNC_项目介绍 = "项目介绍";
    public static final String AI_FUNC_名词解释 = "名词解释";
    public static final String AI_FUNC_异常汇总排序 = "异常汇总排序";
    public static final String AI_FUNC_心理结论 = "心理结论";

    public static final int BARCODE_STATUS_待采样 = 0;
    public static final int BARCODE_STATUS_已采样 = 1;
    public static final int BARCODE_STATUS_已取走 = 2;
    public static final int BARCODE_STATUS_已放弃 = 8;
    public static final int BARCODE_STATUS_已作废 = 9;

    public static final String SMS_NOTIFY_MODULE_团检接洽 = "团检接洽";
    public static final String SMS_NOTIFY_MODULE_危急值管理 = "危急值管理";

    public static final String SMS_BIZ_TYPE_心理测评通知 = "心理测评通知";
    public static final String SMS_BIZ_TYPE_预约到检通知 = "预约到检通知";

}
