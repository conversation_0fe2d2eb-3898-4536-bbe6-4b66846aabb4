# Windows环境Chrome配置指南

## 问题描述
在Windows环境下，Chrome启动失败的常见错误：
```
Chrome failed to start: was killed.
DevToolsActivePort file doesn't exist
```

## 解决方案

### 1. 检查Chrome安装路径
确认Chrome可执行文件的正确路径：
```
E:\project\BJ\chrome-win64\chrome-win64\chrome.exe
```

### 2. 检查ChromeDriver版本匹配
确保ChromeDriver版本与Chrome版本匹配：
```bash
# 查看Chrome版本
E:\project\BJ\chrome-win64\chrome-win64\chrome.exe --version

# 查看ChromeDriver版本
E:\project\BJ\chromedriver.exe --version
```

### 3. 配置application.yml
```yaml
selenium:
  chromeDriverPath: E:/project/BJ/chromedriver.exe
  chromePath: E:/project/BJ/chrome-win64/chrome-win64/chrome.exe
```

### 4. 临时目录权限
确保Java临时目录有写权限：
```bash
# 检查临时目录
echo %TEMP%

# 确保目录可写
```

### 5. 防火墙和安全软件
- 将Chrome和ChromeDriver添加到防火墙白名单
- 暂时关闭实时保护测试

## 测试验证

### 1. 运行测试类
```bash
# 编译项目
mvn clean compile

# 运行测试
java -cp target/classes;target/test-classes org.jeecg.modules.summary.service.impl.WebDriverPoolManagerTest
```

### 2. 手动验证Chrome启动
```bash
# 测试Chrome无头模式启动
E:\project\BJ\chrome-win64\chrome-win64\chrome.exe --headless=new --disable-gpu --no-sandbox --remote-debugging-port=9222

# 如果成功，应该看到Chrome进程在任务管理器中运行
```

### 3. 检查端口占用
```bash
# 检查9222端口是否被占用
netstat -an | findstr 9222
```

## 常见问题排查

### 问题1: Chrome路径包含空格
**解决方案**: 使用短路径名或引号包围路径
```yaml
selenium:
  chromePath: "E:/project/BJ/chrome-win64/chrome-win64/chrome.exe"
```

### 问题2: 用户数据目录权限问题
**解决方案**: 使用Java临时目录
```java
String tempDir = System.getProperty("java.io.tmpdir");
String userDataDir = tempDir + File.separator + "chrome_" + System.currentTimeMillis();
```

### 问题3: Chrome进程残留
**解决方案**: 手动清理Chrome进程
```bash
# 查看Chrome进程
tasklist | findstr chrome

# 强制结束Chrome进程
taskkill /F /IM chrome.exe
```

### 问题4: ChromeDriver版本不匹配
**解决方案**: 下载匹配的ChromeDriver版本
1. 访问 https://chromedriver.chromium.org/
2. 根据Chrome版本下载对应的ChromeDriver
3. 替换现有的chromedriver.exe

## 优化建议

### 1. 使用相对路径
将Chrome和ChromeDriver放在项目目录下：
```
project/
├── drivers/
│   ├── chromedriver.exe
│   └── chrome/
│       └── chrome.exe
```

### 2. 环境变量配置
设置系统环境变量：
```bash
set CHROME_PATH=E:\project\BJ\chrome-win64\chrome-win64\chrome.exe
set CHROMEDRIVER_PATH=E:\project\BJ\chromedriver.exe
```

### 3. 配置文件分离
创建不同环境的配置文件：
- application-dev.yml (开发环境)
- application-test.yml (测试环境)
- application-prod.yml (生产环境)

## 监控和日志

### 1. 启用详细日志
```yaml
logging:
  level:
    org.jeecg.modules.summary.service.impl.WebDriverPoolManager: DEBUG
    org.openqa.selenium: DEBUG
```

### 2. 监控Chrome进程
```bash
# 定期检查Chrome进程数量
for /l %i in (1,0,2) do (
    echo %date% %time%
    tasklist | findstr chrome | find /c "chrome.exe"
    timeout /t 30
)
```

### 3. 内存使用监控
```bash
# 监控Chrome内存使用
wmic process where name="chrome.exe" get ProcessId,PageFileUsage
```

## 应急处理

### 1. Chrome无法启动时的应急方案
```java
// 在WebDriverPoolManager中已实现三级重试机制：
// 1. 标准配置
// 2. 简化配置  
// 3. 基础配置
```

### 2. 进程清理脚本
```batch
@echo off
echo 清理Chrome进程...
taskkill /F /IM chrome.exe /T
taskkill /F /IM chromedriver.exe /T
echo 清理完成
```

### 3. 重启WebDriver池
```java
// 通过管理接口重启池
poolManager.forceCleanupAllChromeProcesses();
```

通过以上配置和优化，应该能够解决Windows环境下Chrome启动失败的问题。
