package org.jeecg.modules.station.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

import java.util.List;

/**
 * @Description: 体检小项结果表
 * @Author: jeecg-boot
 * @Date: 2024-04-21
 * @Version: V1.0
 */
public interface CustomerRegItemResultMapper extends BaseMapper<CustomerRegItemResult> {
    List<CustomerRegItemGroup> listCustomerRegGroupByRegId(@Param("regId") String regId, @Param("departmentIds") List<String> departmentIds,@Param("containFeeOnly") Boolean containFeeOnly);

    List<ItemInfo> listItemByCustomerGroup(@Param("groupId") String groupId, @Param("checkPartCode") String checkPartCode, @Param("regId") String regId,@Param("checkStatus") String checkStatus);

    List<CustomerRegItemResult> listItemResultByReg(@Param("regId") String regId);

    List<CustomerRegItemResult> listByRegIdAndDepartId(@Param("regId") String regId, @Param("departmentIds") List<String> departmentIds);

    List<CustomerRegItemResult> listByRegId(@Param("regId") String regId);

    List<CustomerRegItemResult> listHistoryResultByIdCard(@Param("idCard") String idCard, @Param("years") int years);

    List<String> listHistoryAbnormaoResultIdsByIdCard(@Param("idCard") String idCard, @Param("years") int years);

    List<CustomerRegItemResult> listKeyHistoryResultByIdCard(@Param("idCard") String idCard, @Param("years") int years);

    List<CustomerRegItemResult> getByIdcard(@Param("idCard") String idCard);

    List<CustomerRegItemResult> listAll();

    List<CustomerRegItemResult> getItemResultByReg(@Param("customerRegId")String customerRegId);
}
