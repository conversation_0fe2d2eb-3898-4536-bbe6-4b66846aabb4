package org.jeecg.modules.station.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.UrlUtils;
import org.jeecg.modules.basicinfo.entity.Template;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.basicinfo.service.ITemplateService;
import org.jeecg.modules.mobile.utils.FileUrlUtils;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.bo.ElecAudioOriginData;
import org.jeecg.modules.station.bo.ElecAudioReport;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.station.service.IElecAudioReportService;
import org.jeecg.modules.summary.service.impl.WebDriverPoolManager;
import org.jeecg.modules.summary.service.impl.WebDriverConcurrencyController;
import org.jeecg.modules.summary.service.impl.WebDriverExceptionHandler;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.logging.LogType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Description: 危急值处理记录
 * @Author: jeecg-boot
 * @Date:   2024-05-14
 * @Version: V1.0
 */
@Service
@Slf4j
public class ElecAudioReportServiceImpl implements IElecAudioReportService {
    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ObjectMapper objectMapper; // 注入Spring配置的ObjectMapper
    @Autowired
    private ITemplateService templateService;

    @Autowired
    private WebDriverPoolManager webDriverPoolManager;

    @Autowired
    private WebDriverConcurrencyController concurrencyController;

    @Autowired
    private WebDriverExceptionHandler exceptionHandler;


    @Override
    public ElecAudioReport getElecAudioReportData(String examNo) {
        ElecAudioReport elecAudioReport = new ElecAudioReport();
        CustomerReg customerReg = customerRegService.getByExamNo(examNo);
        if (Objects.isNull(customerReg)) {
            return null;
        }

        elecAudioReport.setName(customerReg.getName());
        elecAudioReport.setGender(customerReg.getGender());
        elecAudioReport.setAge(customerReg.getAge());
        String elecAudioItemGroupId = sysSettingService.getValueByCode("elec_audio_item_group_id");
        List<CustomerRegItemResult> resultList = customerRegItemResultService.list(new LambdaQueryWrapper<CustomerRegItemResult>().eq(CustomerRegItemResult::getExamNo, examNo).eq(CustomerRegItemResult::getItemGroupId, elecAudioItemGroupId));
        log.error("resultList:{}", JSON.toJSONString(resultList));
        if (resultList.isEmpty()) {
            return null;
        }
        Map<String, Object> rowDataMap = null;
        try {
            // 先检查表是否存在以及数据情况
            log.info("开始查询电测听原始数据，examNo: {}", examNo);

            // 检查表中是否有该examNo的数据
            Integer count = jdbcTemplate.queryForObject("select count(*) from audiometry_raw_data where exam_no=?", Integer.class, examNo);
            log.info("audiometry_raw_data表中examNo={}的记录数: {}", examNo, count);

            if (count != null && count > 0) {
                rowDataMap = jdbcTemplate.queryForMap("select * from audiometry_raw_data where exam_no=? limit 1", examNo);
                log.info("成功查询到数据，字段: {}", rowDataMap != null ? rowDataMap.keySet() : "null");
            } else {
                log.warn("audiometry_raw_data表中未找到examNo={}的数据", examNo);
            }
        } catch (Exception e) {
            log.error("查询audiometry_raw_data表异常，examNo: {}, 错误: {}", examNo, e.getMessage(), e);
        }
        if (MapUtils.isEmpty(rowDataMap)) {
            log.warn("rowDataMap为空，无法获取电测听原始数据，examNo: {}", examNo);
            return null;
        }

        log.info("rowDataMap内容: {}", rowDataMap);
        String rowDataJson = MapUtils.getString(rowDataMap, "raw_data_json");
        log.info("rowDataJson: {}", rowDataJson);

        if (StringUtils.isBlank(rowDataJson)) {
            log.warn("raw_data_json字段为空，examNo: {}", examNo);
            return null;
        }
        Object createTimeObj = rowDataMap.get("create_time");
        String deviceName = MapUtils.getString(rowDataMap, "device_model");

        // 处理检查时间
        if (createTimeObj != null) {
            if (createTimeObj instanceof Date) {
                elecAudioReport.setCheckTime((Date) createTimeObj);
            } else if (createTimeObj instanceof String) {
                try {
                    // 如果是字符串，尝试解析为时间戳
                    long timestamp = Long.parseLong((String) createTimeObj);
                    elecAudioReport.setCheckTime(new Date(timestamp));
                } catch (NumberFormatException e) {
                    log.warn("无法解析create_time: {}", createTimeObj);
                    elecAudioReport.setCheckTime(new Date());
                }
            } else {
                elecAudioReport.setCheckTime(new Date());
            }
        } else {
            elecAudioReport.setCheckTime(new Date());
        }
        elecAudioReport.setCheckDoctorName(resultList.get(0).getDoctorName());
        String openFileUrl = sysSettingService.getValueByCode("open_file_url");
        String signPicUrl = FileUrlUtils.replaceUrl(getSignPicByUserId(resultList.get(0).getDoctorId()), openFileUrl);
        elecAudioReport.setCheckDoctorSignPic(signPicUrl);
        elecAudioReport.setDeviceName(deviceName);
        elecAudioReport.setReportNo(resultList.get(0).getExamNo());

        ElecAudioOriginData elecAudioOriginData;
        try {
            elecAudioOriginData = JSONObject.parseObject(rowDataJson, ElecAudioOriginData.class);
            log.info("成功解析ElecAudioOriginData: {}", JSON.toJSONString(elecAudioOriginData));
        } catch (Exception e) {
            log.error("解析rowDataJson失败，examNo: {}, 错误: {}", examNo, e.getMessage(), e);
            return null;
        }

        if (elecAudioOriginData == null) {
            log.warn("解析后的ElecAudioOriginData为null，examNo: {}", examNo);
            return null;
        }

        // 创建频率数组
        String[] frequencies = {"500Hz", "1000Hz", "2000Hz", "3000Hz", "4000Hz", "6000Hz"};
        String[] finalFrequencies = {"0.5", "1", "2", "3", "4", "6"};

        // 构建修正后数据的映射表，根据itemName匹配
        Map<String, String> correctionAfterMap = new HashMap<>();
        for (CustomerRegItemResult result : resultList) {
            if (result.getItemName() != null && result.getValue() != null) {
                correctionAfterMap.put(result.getItemName(), result.getValue());
                log.info("添加修正后数据映射: {} -> {}", result.getItemName(), result.getValue());
            }
        }
        log.info("correctionAfterMap总数: {}, 内容: {}", correctionAfterMap.size(), correctionAfterMap);

        //构建左耳修正前的骨导，气导数据
        List<ElecAudioReport.AudioCheckData> leftCorrectionBeforeCheckDataList = buildCorrectionBeforeCheckDataList(
                finalFrequencies, elecAudioOriginData.getLeftEarAirData(),elecAudioOriginData.getLeftEarBoneData());

      /*  // 构建左耳气导数据
        List<ElecAudioReport.AudioCheckData> leftAirAudioCheckDataList = buildAudioCheckDataList(
            frequencies, elecAudioOriginData.getLeftEarAirData(), correctionAfterMap, "左耳气导");*/

        //构建右耳修正前的骨导，气导数据
        List<ElecAudioReport.AudioCheckData> rightCorrectionBeforeCheckDataList = buildCorrectionBeforeCheckDataList(
                finalFrequencies, elecAudioOriginData.getRightEarAirData(),elecAudioOriginData.getRightEarBoneData());

       /* // 构建右耳气导数据
        List<ElecAudioReport.AudioCheckData> rightAirAudioCheckDataList = buildAudioCheckDataList(
            frequencies, elecAudioOriginData.getRightEarAirData(), correctionAfterMap, "右耳气导");*/

        //构建左耳修正后的骨导，气导数据
        List<ElecAudioReport.AudioCheckData> leftCorrectionAfterCheckDataList = buildCorrectionAfterCheckDataList(
                frequencies, finalFrequencies,correctionAfterMap,"左耳");

     /*   // 构建左耳骨导数据
        List<ElecAudioReport.AudioCheckData> leftBoneAudioCheckDataList = buildAudioCheckDataList(
            frequencies, elecAudioOriginData.getLeftEarBoneData(), correctionAfterMap, "左耳骨导");*/
        //构建左耳修正后的骨导，气导数据
        List<ElecAudioReport.AudioCheckData> rightCorrectionAfterCheckDataList = buildCorrectionAfterCheckDataList(
                frequencies, finalFrequencies,correctionAfterMap,"右耳");
     /*   // 构建右耳骨导数据
        List<ElecAudioReport.AudioCheckData> rightBoneAudioCheckDataList = buildAudioCheckDataList(
            frequencies, elecAudioOriginData.getRightEarBoneData(), correctionAfterMap, "右耳骨导");*/

        elecAudioReport.setLeftCorrectionBeforeData(leftCorrectionBeforeCheckDataList);
        elecAudioReport.setRightCorrectionBeforeData(rightCorrectionBeforeCheckDataList);
        elecAudioReport.setLeftCorrectionAfterData(leftCorrectionAfterCheckDataList);
        elecAudioReport.setRightCorrectionAfterData(rightCorrectionAfterCheckDataList);
        elecAudioReport.setRightWhisperFrequencyAirAvg(correctionAfterMap.get("右耳语频平均听阈（气导）"));
        elecAudioReport.setLeftWhisperFrequencyAirAvg(correctionAfterMap.get("左耳语频平均听阈（气导）"));
        elecAudioReport.setBothHighFrequencyAirAvg(correctionAfterMap.get("双耳高频平均听阈（气导）"));

        return elecAudioReport;
    }
    /**
     * 构建AudioCheckData列表
     * @param frequencies 频率数组
     * @param earAirData 气导数据
     * @param earBoneData 骨导数据

     * @return AudioCheckData列表
     */
    private List<ElecAudioReport.AudioCheckData>  buildCorrectionBeforeCheckDataList(
            String[] frequencies,
            ElecAudioOriginData.EarData earAirData,
            ElecAudioOriginData.EarData earBoneData
            ) {

        List<ElecAudioReport.AudioCheckData> audioCheckDataList = Lists.newArrayList();

        if (earAirData == null) {
            return audioCheckDataList;
        }

        // 获取各频率的修正前数据
        Integer[] correctionBeforeAirValues = {
                earAirData.getHearingThreshold500Hz(),
                earAirData.getHearingThreshold1000Hz(),
                earAirData.getHearingThreshold2000Hz(),
                earAirData.getHearingThreshold3000Hz(),
                earAirData.getHearingThreshold4000Hz(),
                earAirData.getHearingThreshold6000Hz()
        };
        Integer[] correctionBeforeBoneValues = {
                earBoneData.getHearingThreshold500Hz(),
                earBoneData.getHearingThreshold1000Hz(),
                earBoneData.getHearingThreshold2000Hz(),
                earBoneData.getHearingThreshold3000Hz(),
                earBoneData.getHearingThreshold4000Hz(),
                earBoneData.getHearingThreshold6000Hz()
        };

        // 为每个频率创建AudioCheckData
        for (int i = 0; i < frequencies.length; i++) {
            ElecAudioReport.AudioCheckData audioCheckData = new ElecAudioReport.AudioCheckData();

            // 设置频率标识
            audioCheckData.setHearingThreshold(frequencies[i]);

            // 设置修正前气导数据
            audioCheckData.setAirResult(
                    correctionBeforeAirValues[i] != null ? correctionBeforeAirValues[i].toString() : null
            );
            // 设置修正前骨导数据
            audioCheckData.setBoneResult(
                    correctionBeforeBoneValues[i] != null ? correctionBeforeBoneValues[i].toString() : null
            );
            // 设置修正前最终数据，有骨导用骨导数据，没骨导用气导数据
            audioCheckData.setFinalResult(
                    correctionBeforeBoneValues[i] != null ? correctionBeforeBoneValues[i].toString() : (correctionBeforeAirValues[i] != null ? correctionBeforeAirValues[i].toString() : null)
            );

            audioCheckDataList.add(audioCheckData);
        }

        return audioCheckDataList;
    }
    /**
     * 构建AudioCheckData列表
     * @param frequencies 频率数组
     * @param correctionAfterMap 修正后数据映射
     * @param earType 耳朵类型（用于匹配itemName）
     * @return AudioCheckData列表
     */
    private List<ElecAudioReport.AudioCheckData>  buildCorrectionAfterCheckDataList(
            String[] frequencies,
            String[] finalFrequencies,
            Map<String, String> correctionAfterMap,
            String earType) {

        List<ElecAudioReport.AudioCheckData> audioCheckDataList = Lists.newArrayList();


        // 为每个频率创建AudioCheckData
        for (int i = 0; i < frequencies.length; i++) {
            ElecAudioReport.AudioCheckData audioCheckData = new ElecAudioReport.AudioCheckData();

            // 设置频率标识
            audioCheckData.setHearingThreshold(finalFrequencies[i]);


            // 设置修正后气导数据数据 - 根据itemName匹配
            String airData = findCorrectionAfterValueByEarSide(correctionAfterMap, earType, "（气导）",frequencies[i]);
            String boneData = findCorrectionAfterValueByEarSide(correctionAfterMap, earType, "（骨导）",frequencies[i]);
            audioCheckData.setAirResult(airData);
            audioCheckData.setBoneResult(boneData);
            audioCheckData.setFinalResult(StringUtils.isNotBlank(boneData) ? boneData :(StringUtils.isNotBlank(airData) ? airData : null));

            audioCheckDataList.add(audioCheckData);
        }

        return audioCheckDataList;
    }
    /**
     * 构建AudioCheckData列表
     * @param frequencies 频率数组
     * @param earData 耳朵数据
     * @param correctionAfterMap 修正后数据映射
     * @param earType 耳朵类型（用于匹配itemName）
     * @return AudioCheckData列表
     */
    private List<ElecAudioReport.AudioCheckData>  buildAudioCheckDataList(
            String[] frequencies,
            ElecAudioOriginData.EarData earData,
            Map<String, String> correctionAfterMap,
            String earType) {

        List<ElecAudioReport.AudioCheckData> audioCheckDataList = Lists.newArrayList();

        if (earData == null) {
            return audioCheckDataList;
        }

        // 获取各频率的修正前数据
        Integer[] correctionBeforeValues = {
            earData.getHearingThreshold500Hz(),
            earData.getHearingThreshold1000Hz(),
            earData.getHearingThreshold2000Hz(),
            earData.getHearingThreshold3000Hz(),
            earData.getHearingThreshold4000Hz(),
            earData.getHearingThreshold6000Hz()
        };

        // 为每个频率创建AudioCheckData
        for (int i = 0; i < frequencies.length; i++) {
            ElecAudioReport.AudioCheckData audioCheckData = new ElecAudioReport.AudioCheckData();

            // 设置频率标识
            audioCheckData.setHearingThreshold(frequencies[i]);

            // 设置修正前数据
            audioCheckData.setCorrectionBefore(
                correctionBeforeValues[i] != null ? correctionBeforeValues[i].toString() : null
            );

            // 设置修正后数据 - 根据itemName匹配
            String correctionAfter = findCorrectionAfterValue(correctionAfterMap, earType, frequencies[i]);
            audioCheckData.setCorrectionAfter(correctionAfter);

            audioCheckDataList.add(audioCheckData);
        }

        return audioCheckDataList;
    }
    /**
     * 根据耳朵类型和频率查找修正后的值
     * @param correctionAfterMap 修正后数据映射
     * @param earSide 耳朵类型（左、右耳）
     * @param frequency 频率
     * @return 修正后的值
     */
    private String findCorrectionAfterValueByEarSide(Map<String, String> correctionAfterMap, String earSide, String earType,String frequency) {
        // 根据实际项目名称格式进行匹配
        // 实际格式: "右耳1000Hz（骨导）", "左耳500Hz（气导）"

        // 1. 构建期望的itemName格式
        String expectedItemName = earSide + frequency + earType;

        log.debug("查找修正后数据: earType={}, frequency={}, 期望的itemName={}", earType, frequency, expectedItemName);

        // 2. 精确匹配
        String value = correctionAfterMap.get(expectedItemName);
        if (value != null) {
            log.debug("精确匹配成功: {} -> {}", expectedItemName, value);
            return value;
        }

        // 3. 模糊匹配 - 查找包含耳朵、频率和传导类型的项目
        String frequencyNumber = frequency.replace("Hz", "");
        for (Map.Entry<String, String> entry : correctionAfterMap.entrySet()) {
            String itemName = entry.getKey();

            // 检查是否包含正确的耳朵、频率和传导类型
            if (itemName.contains(earSide) &&
                    itemName.contains(frequencyNumber) &&
                    itemName.contains(earType)) {
                log.debug("模糊匹配成功: {} -> {}", itemName, entry.getValue());
                return entry.getValue();
            }
        }

        // 4. 更宽松的匹配 - 只匹配耳朵和频率
        for (Map.Entry<String, String> entry : correctionAfterMap.entrySet()) {
            String itemName = entry.getKey();

            if (itemName.contains(earSide) && itemName.contains(frequencyNumber)) {
                // 进一步检查传导类型
                boolean isAirConduction = itemName.contains("气导") || itemName.contains("气");
                boolean isBoneConduction = itemName.contains("骨导") || itemName.contains("骨");

                if ((earType.contains("气导") && isAirConduction) ||
                        (earType.contains("骨导") && isBoneConduction)) {
                    log.debug("宽松匹配成功: {} -> {}", itemName, entry.getValue());
                    return entry.getValue();
                }
            }
        }

        log.warn("未找到匹配的修正后数据: earType={}, frequency={}", earType, frequency);
        return null;
    }
    /**
     * 根据耳朵类型和频率查找修正后的值
     * @param correctionAfterMap 修正后数据映射
     * @param earType 耳朵类型
     * @param frequency 频率
     * @return 修正后的值
     */
    private String findCorrectionAfterValue(Map<String, String> correctionAfterMap, String earType, String frequency) {
        // 根据实际项目名称格式进行匹配
        // 实际格式: "右耳1000Hz（骨导）", "左耳500Hz（气导）"

        // 1. 构建期望的itemName格式
        String earSide = earType.contains("左耳") ? "左耳" : "右耳";
        String conductionType = earType.contains("气导") ? "（气导）" : "（骨导）";
        String expectedItemName = earSide + frequency + conductionType;

        log.debug("查找修正后数据: earType={}, frequency={}, 期望的itemName={}", earType, frequency, expectedItemName);

        // 2. 精确匹配
        String value = correctionAfterMap.get(expectedItemName);
        if (value != null) {
            log.debug("精确匹配成功: {} -> {}", expectedItemName, value);
            return value;
        }

        // 3. 模糊匹配 - 查找包含耳朵、频率和传导类型的项目
        String frequencyNumber = frequency.replace("Hz", "");
        for (Map.Entry<String, String> entry : correctionAfterMap.entrySet()) {
            String itemName = entry.getKey();

            // 检查是否包含正确的耳朵、频率和传导类型
            if (itemName.contains(earSide) &&
                itemName.contains(frequencyNumber) &&
                itemName.contains(conductionType)) {
                log.debug("模糊匹配成功: {} -> {}", itemName, entry.getValue());
                return entry.getValue();
            }
        }

        // 4. 更宽松的匹配 - 只匹配耳朵和频率
        for (Map.Entry<String, String> entry : correctionAfterMap.entrySet()) {
            String itemName = entry.getKey();

            if (itemName.contains(earSide) && itemName.contains(frequencyNumber)) {
                // 进一步检查传导类型
                boolean isAirConduction = itemName.contains("气导") || itemName.contains("气");
                boolean isBoneConduction = itemName.contains("骨导") || itemName.contains("骨");

                if ((earType.contains("气导") && isAirConduction) ||
                    (earType.contains("骨导") && isBoneConduction)) {
                    log.debug("宽松匹配成功: {} -> {}", itemName, entry.getValue());
                    return entry.getValue();
                }
            }
        }

        log.warn("未找到匹配的修正后数据: earType={}, frequency={}", earType, frequency);
        return null;
    }



    public String getSignPicByUserId(String userId) {
        String signPic = null;
        try {
            signPic = jdbcTemplate.queryForObject("select sign_pic from sys_user where id = ?", String.class, userId);
        } catch (Exception e) {
            log.error("根据用户名获取系统用户签名图片异常，用户名：" + userId);
        }
        return signPic;
    }


    @Override
    public void generateElecAudioReportPdf() {

        String elecAudioItemGroupId = sysSettingService.getValueByCode("elec_audio_item_group_id");
        if (elecAudioItemGroupId == null) {
            return;
        }

        LambdaQueryWrapper<CustomerRegItemGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegItemGroup::getItemGroupId, elecAudioItemGroupId);
        queryWrapper.eq(CustomerRegItemGroup::getCheckStatus, ExConstants.CHECK_STATUS_已检);
        queryWrapper.eq(CustomerRegItemGroup::getPicSyncStatus, "0");
        queryWrapper.eq(CustomerRegItemGroup::getPayStatus, "已支付");
        queryWrapper.last("limit 10000");


        List<CustomerRegItemGroup> customerRegItemGroupList = customerRegItemGroupService.list(queryWrapper);
        log.info("Generating PDF reports for " + customerRegItemGroupList.size() + " records...");
        if (customerRegItemGroupList.isEmpty()) {
            return;
        }

        String url = sysSettingService.getValueByCode("reportGenerateUrl");
        String localFileServerDomain = sysSettingService.getValueByCode("local_file_url");

        for (CustomerRegItemGroup customerRegItemGroup : customerRegItemGroupList) {
            generateElecAudioReportPdf4CustomerReg(customerRegItemGroup, url, localFileServerDomain, true);
        }
    }


    @Override
    public void generateElecAudioReportPdf4CustomerReg(CustomerRegItemGroup customerRegItemGroup, String url, String localFileServerDomain, boolean updateStatus) {
        String examNo = customerRegItemGroup.getExamNo();
        String operationName = "generateElecAudioReportPdf_" + examNo;

        try {
            // 使用并发控制器执行PDF生成操作
            concurrencyController.executeWithConcurrencyControl(operationName, () -> {
                return generateElecAudioPdfInternal(customerRegItemGroup, url, localFileServerDomain, updateStatus);
            });

        } catch (Exception e) {
            log.error("Error generating PDF report for examNo: {}", examNo, e);
            throw new RuntimeException("Error occurred while generating PDF report: " + e.getMessage(), e);
        }
    }

    /**
     * 内部电测听PDF生成方法
     */
    private Void generateElecAudioPdfInternal(CustomerRegItemGroup customerRegItemGroup, String url, String localFileServerDomain, boolean updateStatus) throws Exception {
        WebDriver driver = null;
        String examNo = customerRegItemGroup.getExamNo();

        try {
            // 获取WebDriver
            log.info("Getting WebDriver instance for examNo: {}", examNo);
            driver = webDriverPoolManager.borrowWebDriver(url, "电测听PDF生成", examNo);
            JavascriptExecutor js = (JavascriptExecutor) driver;

            // 检查页面状态
            log.info("Checking page status...");
            String readyState = (String) js.executeScript("return document.readyState");
            log.info("Page status: {}", readyState);

            // 获取当前页面的基础URL，用于构建绝对路径
            String baseUrl = (String) js.executeScript(
                    "return window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1);"
            );
            log.info("Base URL: {}", baseUrl);

            // 检查脚本是否已加载
            log.info("Checking if scripts are loaded...");
            Boolean hasCore = false;
            Boolean hasPdf = false;

            // 尝试多次检查脚本加载状态
            for (int i = 0; i < 10; i++) {
                try {
                    hasCore = (Boolean) js.executeScript(
                            "return typeof MESCIUS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS.Core !== 'undefined'");

                    hasPdf = (Boolean) js.executeScript(
                            "return typeof MESCIUS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                    "typeof MESCIUS.ActiveReportsJS.PdfExport !== 'undefined'");

                    if (hasCore && hasPdf) {
                        log.info("Scripts loaded successfully");
                        break;
                    }

                    log.info("Scripts not fully loaded, waiting... (Core: {}, PDF: {})", hasCore, hasPdf);
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.warn("Error checking script loading status: {}", e.getMessage());
                    Thread.sleep(1000);
                }
            }

            // 如果脚本未加载，尝试手动加载
            if (!hasCore || !hasPdf) {
                log.info("Attempting to manually load scripts...");

                // 清除现有脚本
                js.executeScript(
                        "var scripts = document.querySelectorAll('script[src*=\"ar-js\"]');" +
                                "scripts.forEach(function(script) { script.parentNode.removeChild(script); });"
                );

                // 使用绝对路径添加核心脚本
                String coreScriptUrl = baseUrl + "scripts/ar-js-core.js";
                log.info("Loading core script: {}", coreScriptUrl);
                js.executeScript(
                        "var coreScript = document.createElement('script');" +
                                "coreScript.src = arguments[0];" +
                                "document.head.appendChild(coreScript);",
                        coreScriptUrl
                );

                // 等待核心脚本加载
                log.info("Waiting for core script to load...");
                long startTime = System.currentTimeMillis();
                while (true) {
                    try {
                        hasCore = (Boolean) js.executeScript(
                                "return typeof MESCIUS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS.Core !== 'undefined'");

                        if (hasCore) {
                            log.info("Core script loaded successfully");
                            break;
                        }

                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("Core script loading timeout");
                            throw new RuntimeException("Core script loading timeout");
                        }

                        Thread.sleep(1000);
                    } catch (Exception e) {
                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("Core script loading timeout: {}", e.getMessage());
                            throw new RuntimeException("Core script loading timeout: " + e.getMessage());
                        }
                        Thread.sleep(1000);
                    }
                }

                // 使用绝对路径添加PDF脚本
                String pdfScriptUrl = baseUrl + "scripts/ar-js-pdf.js";
                log.info("Loading PDF script: {}", pdfScriptUrl);
                js.executeScript(
                        "var pdfScript = document.createElement('script');" +
                                "pdfScript.src = arguments[0];" +
                                "document.head.appendChild(pdfScript);",
                        pdfScriptUrl
                );

                // 等待PDF脚本加载
                log.info("Waiting for PDF script to load...");
                startTime = System.currentTimeMillis();
                while (true) {
                    try {
                        hasPdf = (Boolean) js.executeScript(
                                "return typeof MESCIUS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                        "typeof MESCIUS.ActiveReportsJS.PdfExport !== 'undefined'");

                        if (hasPdf) {
                            log.info("PDF script loaded successfully");
                            break;
                        }

                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("PDF script loading timeout");
                            throw new RuntimeException("PDF script loading timeout");
                        }

                        Thread.sleep(1000);
                    } catch (Exception e) {
                        if (System.currentTimeMillis() - startTime > 20000) {
                            log.error("PDF script loading timeout: {}", e.getMessage());
                            throw new RuntimeException("PDF script loading timeout: " + e.getMessage());
                        }
                        Thread.sleep(1000);
                    }
                }
            }

            // 注册字体 - 使用绝对路径
            log.info("Registering fonts...");
            try {
                String fontConfigUrl = baseUrl + "fontsConfig.json";
                log.info("Font config URL: {}", fontConfigUrl);
                js.executeScript(
                        "if (typeof MESCIUS !== 'undefined' && " +
                                "    typeof MESCIUS.ActiveReportsJS !== 'undefined' && " +
                                "    typeof MESCIUS.ActiveReportsJS.Core !== 'undefined' && " +
                                "    typeof MESCIUS.ActiveReportsJS.Core.FontStore !== 'undefined') {" +
                                "  MESCIUS.ActiveReportsJS.Core.FontStore.registerFonts(arguments[0]);" +
                                "}",
                        fontConfigUrl
                );

                // 等待字体注册
                Thread.sleep(2000);
            } catch (Exception e) {
                log.warn("Font registration may have failed: {}", e.getMessage());
            }

            // 处理单个CustomerReg的逻辑
            log.info("Preparing report data...");
            ElecAudioReport elecAudioReportData = getElecAudioReportData(examNo);
            //处理医生签名图片
            if (StringUtils.isNotBlank(elecAudioReportData.getCheckDoctorSignPic()) && !elecAudioReportData.getCheckDoctorSignPic().startsWith("http")) {
                String newSignPic = UrlUtils.concatenateUrl(localFileServerDomain, elecAudioReportData.getCheckDoctorSignPic());
                elecAudioReportData.setCheckDoctorSignPic(newSignPic);
            }
            String reportBeanJson = objectMapper.writeValueAsString(elecAudioReportData);
            Template reportTemplate = getElecAudioReportTemplate();

            if (reportTemplate == null) {
                throw new RuntimeException("Report template not found");
            }

            log.info("Report template ID: {}, content length: {}",
                    reportTemplate.getId(),
                    reportTemplate.getContent() != null ? reportTemplate.getContent().length() : 0);

            // 使用直接的JavaScript代码生成PDF
            log.info("Executing PDF generation...");
            String script =
                    "var callback = arguments[arguments.length - 1];" +
                            "try {" +
                            "  console.log('Starting PDF generation...');" +
                            "  var template = JSON.parse(arguments[0]);" +
                            "  console.log('Template parsed successfully');" +
                            "  template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + arguments[1];" +
                            "  console.log('Data source configured successfully');" +
                            "  " +
                            "  console.log('Creating report instance...');" +
                            "  var report = new MESCIUS.ActiveReportsJS.Core.PageReport();" +
                            "  " +
                            "  console.log('Loading report...');" +
                            "  report.load(template)" +
                            "    .then(function() {" +
                            "      console.log('Running report...');" +
                            "      return report.run();" +
                            "    })" +
                            "    .then(function(doc) {" +
                            "      console.log('Exporting to PDF...');" +
                            "      return MESCIUS.ActiveReportsJS.PdfExport.exportDocument(doc, {" +
                            "        info: { author: 'MESCIUS' }" +
                            "      });" +
                            "    })" +
                            "    .then(function(result) {" +
                            "      console.log('Reading PDF data...');" +
                            "      var reader = new FileReader();" +
                            "      reader.readAsDataURL(result.data);" +
                            "      reader.onload = function() {" +
                            "        var base64Data = reader.result.split(',')[1];" +
                            "        console.log('PDF generated successfully, data length: ' + base64Data.length);" +
                            "        callback(base64Data);" +
                            "      };" +
                            "      reader.onerror = function(error) {" +
                            "        console.error('Failed to read PDF data', error);" +
                            "        callback('ERROR: Failed to read PDF data');" +
                            "      };" +
                            "    })" +
                            "    .catch(function(error) {" +
                            "      console.error('PDF generation failed', error);" +
                            "      callback('ERROR: ' + (error.message || error));" +
                            "    });" +
                            "} catch (error) {" +
                            "  console.error('Script execution exception', error);" +
                            "  callback('ERROR: ' + (error.message || error));" +
                            "}";

            // 获取浏览器控制台日志
            log.info("Browser console logs:");
            try {
                driver.manage().logs().get(LogType.BROWSER).getAll().forEach(
                        logEntry -> log.info("Browser: {}", logEntry.getMessage())
                );
            } catch (Exception e) {
                log.warn("Unable to get browser logs: {}", e.getMessage());
            }

            // 执行脚本
            log.info("Executing PDF generation script...");
            Object response = js.executeAsyncScript(script, reportTemplate.getContent(), reportBeanJson);

            // 再次获取浏览器控制台日志
            log.info("Browser console logs after script execution:");
            try {
                driver.manage().logs().get(LogType.BROWSER).getAll().forEach(
                        logEntry -> log.info("Browser: {}", logEntry.getMessage())
                );
            } catch (Exception e) {
                log.warn("Unable to get browser logs: {}", e.getMessage());
            }

            // 处理响应
            if (response instanceof String result) {
                if (result.startsWith("ERROR: ")) {
                    log.error("Report generation failed: {}", StringUtils.substring(result, 0, 500));
                    throw new RuntimeException("Report generation failed: " + result);
                } else {
                    log.info("PDF generated successfully, processing result...");
                    handlePdfResult(result,customerRegItemGroup,localFileServerDomain);
                }
            } else {
                log.error("Unexpected JavaScript response: {}", response);
                throw new RuntimeException("Unexpected JavaScript response");
            }
        } catch (Exception e) {
            // 分析异常类型并执行相应的恢复操作
            WebDriverExceptionHandler.WebDriverExceptionType exceptionType =
                exceptionHandler.analyzeException(e);
            WebDriverExceptionHandler.ExceptionHandlingStrategy strategy =
                exceptionHandler.getHandlingStrategy(exceptionType);

            log.error("Error generating PDF report for examNo: {}, exception type: {}, strategy: {}",
                examNo, exceptionType, strategy, e);

            // 执行恢复操作
            if (driver != null) {
                exceptionHandler.executeRecoveryAction(driver, strategy, webDriverPoolManager);
            }

            throw new RuntimeException("Error occurred while generating PDF report: " + e.getMessage(), e);
        } finally {
            if (driver != null) {
                log.info("Returning WebDriver instance for examNo: {}", examNo);
                webDriverPoolManager.returnWebDriver(driver);
            }
        }

        return null; // Void return type
    }

    // 辅助方法：获取报告模板
    private Template getElecAudioReportTemplate() {
            LambdaQueryWrapper<Template> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Template::getType, ExConstants.TEMPLATE_TYPE_报告).eq(Template::getRegType, ExConstants.BUYER_TYPE_个人).like(Template::getName, "电测听报告");
            List<Template> templates = templateService.list(queryWrapper);
            if (!templates.isEmpty()){
                return templates.get(0);
            }
            return null;
    }

    // 辅助方法：处理PDF结果
    private void handlePdfResult(String base64Pdf, CustomerRegItemGroup customerRegItemGroup,String localFileServerDomain) throws Exception {
        byte[] pdfData = java.util.Base64.getDecoder().decode(base64Pdf);
        String eReportUrl = MinioUtil.upload(pdfData, "", customerRegItemGroup.getId() + ".pdf");
        String eReportUrlAllPath = eReportUrl;
        if (StringUtils.isNotBlank(eReportUrl) && !eReportUrl.startsWith("http")) {
            eReportUrlAllPath = UrlUtils.concatenateUrl(localFileServerDomain, eReportUrl);
        }
        List<String> picList = MinioUtil.uploadPdfAsImagesFromUrl(eReportUrlAllPath, "png");

        LambdaUpdateWrapper<CustomerRegItemGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CustomerRegItemGroup::getReportPdf, eReportUrl).set(CustomerRegItemGroup::getPic, JSON.toJSONString(picList)).set(CustomerRegItemGroup::getReportPics, JSON.toJSONString(picList)).eq(CustomerRegItemGroup::getId, customerRegItemGroup.getId());
        updateWrapper.set(CustomerRegItemGroup::getPicSyncStatus, "1");
        customerRegItemGroupService.update(null, updateWrapper);
        //更新result表
        LambdaUpdateWrapper<CustomerRegItemResult> resultUpdateWrapper = new LambdaUpdateWrapper<>();
        resultUpdateWrapper.set(CustomerRegItemResult::getReportPdf, eReportUrl).set(CustomerRegItemResult::getPic, JSON.toJSONString(picList)).eq(CustomerRegItemResult::getCustomerRegId, customerRegItemGroup.getCustomerRegId()).eq(CustomerRegItemResult::getItemGroupId, customerRegItemGroup.getItemGroupId());
        customerRegItemResultService.update(null, resultUpdateWrapper);
    }


}
